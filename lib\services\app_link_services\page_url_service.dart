import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

// Conditional imports for web-specific functionality
import 'web_url_manager_stub.dart'
    if (dart.library.html) 'web_url_manager_web.dart';

/// Universal page URL service that automatically handles web app URL management
/// without requiring manual platform checks or commenting/uncommenting code.
///
/// This service provides a simple API that can be used on any page and will
/// automatically handle URL updates only when running on web platform.
class PageUrlService {
  /// Sets the page URL for web app navigation.
  ///
  /// This method automatically detects if the app is running on web platform
  /// and only updates the URL in that case. For mobile platforms, this method
  /// does nothing, so it's safe to call from any page without platform checks.
  ///
  /// [path] - The URL path to set (e.g., '/help', '/payment', '/profile')
  /// [title] - Optional page title for the browser tab
  ///
  /// Example usage:
  /// ```dart
  /// PageUrlService.setPageUrl('/help', 'Help');
  /// ```
  static void setPageUrl(String path, [String? title]) {
    // Only execute on web platform
    if (kIsWeb) {
      try {
        WebUrlManager.updateUrl(path, title);
      } catch (e) {
        // Silently handle any errors to prevent crashes on mobile
        debugPrint('PageUrlService: Error updating URL - $e');
      }
    }
  }

  /// Sets the page URL with automatic post-frame callback.
  ///
  /// This method is useful when you want to set the URL after the widget
  /// has been built. It automatically schedules the URL update for the
  /// next frame, which is often needed in build methods.
  ///
  /// [path] - The URL path to set (e.g., '/help', '/payment', '/profile')
  /// [title] - Optional page title for the browser tab
  ///
  /// Example usage in a widget's build method:
  /// ```dart
  /// @override
  /// Widget build(BuildContext context) {
  ///   PageUrlService.setPageUrlAfterBuild('/help', 'Help');
  ///   return Scaffold(...);
  /// }
  /// ```
  static void setPageUrlAfterBuild(String path, [String? title]) {
    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setPageUrl(path, title);
      });
    }
  }

  /// Gets the current URL (web only).
  ///
  /// Returns the current browser URL if running on web, otherwise returns
  /// an empty string for mobile platforms.
  ///
  /// Returns: Current URL string or empty string on mobile
  static String getCurrentUrl() {
    if (kIsWeb) {
      try {
        return WebUrlManager.getCurrentUrl();
      } catch (e) {
        debugPrint('PageUrlService: Error getting current URL - $e');
        return '';
      }
    }
    return '';
  }

  /// Checks if URL management is supported on current platform.
  ///
  /// Returns: true if running on web, false otherwise
  static bool get isSupported => kIsWeb;

  /// Initializes URL capture for web app.
  ///
  /// This method should be called once during app initialization to set up
  /// URL capture functionality. It's safe to call on all platforms.
  static void initialize() {
    if (kIsWeb) {
      try {
        WebUrlManager.initializeUrlCapture();
      } catch (e) {
        debugPrint('PageUrlService: Error initializing URL capture - $e');
      }
    }
  }

  /// Sets up URL listener for navigation changes (web only).
  ///
  /// [onUrlChange] - Callback function to execute when URL changes
  static void setupUrlListener(Function? onUrlChange) {
    if (kIsWeb) {
      try {
        WebUrlManager.setupUrlListener(onUrlChange);
      } catch (e) {
        debugPrint('PageUrlService: Error setting up URL listener - $e');
      }
    }
  }

  /// Gets referral code from current URL (web only).
  ///
  /// Returns: Referral code from URL parameters or null
  static String? getReferralCodeFromUrl() {
    if (kIsWeb) {
      try {
        return WebUrlManager.getReferralCodeFromUrl();
      } catch (e) {
        debugPrint('PageUrlService: Error getting referral code - $e');
        return null;
      }
    }
    return null;
  }
}
