import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/product_variant/product_variant_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class ProductVariantService {
  late HttpService httpService;

  ProductVariantService() {
    httpService = HttpService();
  }

  // Get all variants for a product
  Future<ProductVariantResponse> getProductVariants({
    required String productReference,
  }) async {
    try {
      final queryParams = {
        'product_reference': productReference,
      };

      final response = await httpService.getApiCall(
        "${AppConstants.baseUrl}product/productvariants/",
        queryParams: queryParams,
      );

      return ProductVariantResponse.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Create or update a product variant
  Future<CreateVariantResponse> createOrUpdateVariant({
    required String productReference,
    required int mrpPrice,
    required int sellingPrice,
    required int stock,
    required Map<String, String> combinations,
  }) async {
    try {
      final body = {
        'product_reference': productReference,
        'mrp_price': mrpPrice,
        'selling_price': sellingPrice,
        'stock': stock,
        'combinations': combinations,
      };

      final response = await httpService.postApiCall(
        body,
        "${AppConstants.baseUrl}product/productvariants/",
      );

      return CreateVariantResponse.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Batch create variants
  Future<List<CreateVariantResponse>> createVariantsBatch({
    required String productReference,
    required List<ProductVariant> variants,
  }) async {
    try {
      List<CreateVariantResponse> responses = [];
      
      for (var variant in variants) {
        final response = await createOrUpdateVariant(
          productReference: productReference,
          mrpPrice: variant.mrpPrice,
          sellingPrice: variant.sellingPrice,
          stock: variant.stock,
          combinations: variant.combinations,
        );
        responses.add(response);
      }
      
      return responses;
    } catch (e) {
      rethrow;
    }
  }
}
