import 'dart:html' as html;
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';

/// Web implementation for URL management
class WebUrlManager {
  /// Updates the browser URL with the given path and optional title
  static void updateUrl(String path, [String? title]) {
    try {
      html.window.history.pushState(null, title ?? '', path);
    } catch (e) {
      // Silently handle any errors
    }
  }

  /// Gets the current URL
  static String getCurrentUrl() {
    try {
      return html.window.location.href;
    } catch (e) {
      return '';
    }
  }

  /// Checks if the current platform supports URL management
  static bool get isSupported => true;

  /// Initializes URL capture for web app
  static void initializeUrlCapture() {
    try {
      if (AppConstants.webChangedUrl.isEmpty) {
        var url = html.window.location.href;
        // If url contains ic then save in cache
        if (Uri.parse(url).queryParameters['ic'] != null) {
          AppDataService().saveReferralCode(
              referralCode: Uri.parse(url).queryParameters['ic']!);
        }
        AppConstants.webChangedUrl = url;
      }
    } catch (e) {
      // Silently handle any errors
    }
  }

  /// Gets referral code from current URL
  static String? getReferralCodeFromUrl() {
    try {
      var url = html.window.location.href;
      return Uri.parse(url).queryParameters['ic'];
    } catch (e) {
      return null;
    }
  }

  /// Sets up URL listener for navigation changes
  static void setupUrlListener(Function? onUrlChange) {
    try {
      html.window.onPopState.listen((event) {
        if (onUrlChange != null) {
          onUrlChange();
        }
      });
    } catch (e) {
      // Silently handle any errors
    }
  }
}
