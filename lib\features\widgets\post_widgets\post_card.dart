import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/post_and_product_appbar/post_and_product_appbar.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/widgets/tagged_items_widget/tagged_items_widget.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/add_visited_reference/add_visited_references.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_common_image_icon/app_common_image_icon.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/mention_parser/mention_parser.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:swadesic/services/post_service/post_service.dart';

class PostCard extends StatefulWidget {
  final PostDetail postDetail;
  final Function onTapEdit;
  final Function onTapDelete;
  final Function? onTapReport;
  final Function onTapHeart;
  final Function onTapDrawer;
  final Function onTapProfileImage;
  final Function onTapShare;
  final Function onTapPost;
  final bool? isFullView;
  final String? customTitle;
  final bool? isCustomTitleVisible;
  final double imageSize;
  final bool isFromSinglePost;

  const PostCard(
      {super.key,
      required this.postDetail,
      required this.onTapEdit,
      required this.onTapDelete,
      this.onTapReport,
      required this.onTapHeart,
      required this.onTapDrawer,
      required this.onTapProfileImage,
      required this.onTapShare,
      required this.onTapPost,
      this.imageSize = 236,
      this.isFromSinglePost = false,
      this.isFullView = false,
      this.customTitle,
      this.isCustomTitleVisible = false});

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  //region Bloc
  late PostCardBloc postCardBloc;

  //endregion

  //region Init
  @override
  void initState() {
    postCardBloc = PostCardBloc(context, widget.postDetail);
    postCardBloc.init();

    // Set initial dropdown state based on reviewed item type
    _initializeReviewedProductExpandedState();

    super.initState();
  }

  void _initializeReviewedProductExpandedState() {
    // Check if this post has reviewed products
    if (widget.postDetail.reviewedProducts != null &&
        widget.postDetail.reviewedProducts!.isNotEmpty) {
      // Check if it's a reviewed store or reviewed product
      bool isReviewedStore =
          widget.postDetail.reviewedProducts!.first.type?.toLowerCase() ==
              'store';

      // Reviewed product should expand by default, reviewed store should be collapsed
      _isReviewedProductExpanded = !isReviewedStore;
    }
  }

//endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region App bar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        leadingIcon: AppImages.locationIcon,
        isCustomLeadingIcon: true,
        onTapLeading: () {},
        context: context,
        isCenterTitle: true,
        isCustomTitle: true,
        customTitleWidget: const Text("Hello"),
        isDefaultMenuVisible: false);
  }

  //endregion

  //region Body
  Widget body() {
    return VisibilityDetector(
      key: UniqueKey(),
      onVisibilityChanged: (visibilityInfo) {
        var visiblePercentage = visibilityInfo.visibleFraction * 100;
        if (visiblePercentage > 50) {
          //Add to visited post
          //AddVisitedReferences().addReferences(reference: widget.postDetail.postOrCommentReference!);
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          userInfo(),
          InkWell(
            highlightColor: Colors.transparent,
            onTap: () {
              widget.onTapPost();
            },
            onDoubleTap: () {
              //If already liked then return
              if (widget.postDetail.likeStatus!) {
                return;
              }
              widget.onTapHeart();
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                postImage(),
                comment(),
                ratingDisplay(),
                reviewedProductSection(),
                taggedItemsDropdown(),
                action(),
                counts(),
              ],
            ),
          ),
        ],
      ),
    );
  }

//endregion

  //region Widget user info
  Widget userInfo() {
    // Check if this is a review post
    if (widget.postDetail.commentType == 'REVIEW' ||
        widget.postDetail.commentType == 'EXTERNAL_REVIEW' ||
        widget.postDetail.contentHeaderText?.contains('review') == true) {
      return reviewPostHeader();
    }

    return widget.isCustomTitleVisible! && widget.customTitle != null
        ? repostedPostHeader()
        : simplePostHeader();
    // return Container(
    //   alignment: Alignment.centerLeft,
    //   padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
    //   child: Row(
    //     mainAxisSize: MainAxisSize.min,
    //     mainAxisAlignment: MainAxisAlignment.start,
    //     crossAxisAlignment: CrossAxisAlignment.center,
    //     children: [
    //       InkWell(
    //         onTap: () {
    //           widget.onTapProfileImage();
    //         },
    //         child: CustomImageContainer(
    //           width: 32,
    //           height: 32,
    //           imageUrl: widget.postDetail.createdBy!.icon,
    //           imageType: widget.postDetail.createdBy!.entityType == EntityType.USER.name ? CustomImageContainerType.user : CustomImageContainerType.store,
    //         ),
    //         // child: ClipRRect(
    //         //   borderRadius: BorderRadius.circular(widget.postDetail.entityType == EntityType.USER.name?100:(0.4130 * 27)),
    //         //   child: SizedBox(
    //         //     height: 27,
    //         //     width: 27,
    //         //     child: extendedImage(widget.postDetail.icon, context, 100, 100, customPlaceHolder:widget.postDetail.entityType == EntityType.USER.name?AppImages.userPlaceHolder:AppImages.storePlaceHolder),
    //         //   ),
    //         // ),
    //       ),
    //       const SizedBox(
    //         width: 7,
    //       ),
    //       //Handle and time
    //       Expanded(
    //         child: Column(
    //           mainAxisSize: MainAxisSize.min,
    //           mainAxisAlignment: MainAxisAlignment.start,
    //           crossAxisAlignment: CrossAxisAlignment.start,
    //           children: [
    //             Flexible(
    //               child: InkWell(
    //                 onTap: () {
    //                   widget.onTapProfileImage();
    //                 },
    //                 child: Text(
    //                   widget.postDetail.createdBy!.handle!,
    //                   style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    //                   overflow: TextOverflow.ellipsis,
    //                 ),
    //               ),
    //             ),
    //             const SizedBox(
    //               width: 5,
    //             ),
    //             Text(
    //               postCardBloc.convertDateFormat(inputDateTimeString: widget.postDetail.createdDate!),
    //               overflow: TextOverflow.ellipsis,
    //               style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
    //             ),
    //           ],
    //         ),
    //       ),
    //       //Three dots
    //       SizedBox(
    //           height: 18,
    //           width: 18,
    //           child: CupertinoButton(
    //             padding: EdgeInsets.zero,
    //             onPressed: () {
    //               // widget.onTapDrawer();
    //               postCardBloc.onTapDrawer(postDetail: widget.postDetail);
    //             },
    //             child: RotatedBox(
    //                 quarterTurns: 1,
    //                 child: SvgPicture.asset(
    //                   AppImages.commentOption,
    //                   color: AppColors.appBlack,
    //                   height: 18,
    //                 )),
    //           ))
    //     ],
    //   ),
    // );
  }

  //endregion

  //region Post image
  Widget postImage() {
    return widget.postDetail.images!.isNotEmpty
        ? Container(
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(vertical: 5),
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  height: widget.imageSize,
                  child: ListView.builder(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemCount: widget.postDetail.images!.length,
                      itemBuilder: (context, index) {
                        return CupertinoButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              postCardBloc.onTapImage(
                                  index: index,
                                  imageList: widget.postDetail.images!
                                      .map((e) => e.mediaPath!)
                                      .toList());
                            },
                            // child: extendedImage(widget.postDetail.postImages![index].postImage, context, 800, 800),
                            child: PostAndProductImageWidgets(
                              localOrNetworkImage:
                                  widget.postDetail.images![index].mediaPath!,
                              imageSize: widget.imageSize,
                            ));
                      }),
                )))
        : const SizedBox();
  }

  //endregion

  //region Comment
  Widget comment() {
    return Visibility(
      visible:
          widget.postDetail.text != null && widget.postDetail.text!.isNotEmpty,
      child: Container(
          alignment: Alignment.centerLeft,
          // margin: const EdgeInsets.only(left: 15, right: 15, top: 5),
          margin:
              // widget.postDetail.images!.isNotEmpty?
              const EdgeInsets.only(left: 15, right: 15, bottom: 5),
          //
          //     :
          // const EdgeInsets.symmetric(horizontal: 15,vertical: 5),

          child: ReadMoreText(
            widget.postDetail.text!,
            trimMode: TrimMode.Line,
            trimLines: widget.isFullView! ? 1000 : 3,
            colorClickableText: Colors.pink,
            style: widget.isFromSinglePost
                ? AppTextStyle.contentText1(textColor: AppColors.appBlack)
                : AppTextStyle.contentText0(textColor: AppColors.appBlack),
            lessStyle:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            moreStyle:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            trimLength: 5,
            trimCollapsedText: AppStrings.more,
            trimExpandedText: " ${AppStrings.less}",
            textAlign: TextAlign.start,
            annotations: [
              // Annotation(
              //   regExp: RegExp(r'#([a-zA-Z0-9_]+)'),
              //   spanBuilder: ({required String text, TextStyle? textStyle}) => TextSpan(
              //     text: text,
              //     style: textStyle?.copyWith(color: Colors.blue),
              //   ),
              // ),
              //User name or handle
              Annotation(
                regExp: RegExp(r"\{\{mention:\{[^}]*\}\}\}"),
                spanBuilder: ({required String text, TextStyle? textStyle}) {
                  // Extract display text from encoded mention
                  String displayText = MentionParser.extractDisplayText(text);
                  String reference = MentionParser.extractReference(text);

                  return TextSpan(
                    text: displayText,
                    style:
                        AppTextStyle.access0(textColor: AppColors.brandGreen),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // Use the display text for navigation
                        OnTapTag(context, displayText);
                      },
                  );
                },
              ),
              //URL
              Annotation(
                // regExp: RegExp(r'(https?://)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*/?',caseSensitive: true,multiLine: false),
                regExp: AppConstants.urlRegex,

                spanBuilder: ({required String text, TextStyle? textStyle}) =>
                    TextSpan(
                  text: text,
                  style: textStyle?.copyWith(color: AppColors.brandBlack),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      CommonMethods.opeAppWebView(
                          webUrl: text,
                          context:
                              AppConstants.globalNavigator.currentContext!);
                      //print(text); // Print the URL
                    },
                ),
              ),
            ],
          )

          // child: ReadMoreText(
          //   widget.postDetail.text!,
          //   trimLines: 5,
          //   trimMode: TrimMode.Line,
          //   trimCollapsedText: AppStrings.more,
          //   trimExpandedText: " ${AppStrings.less}",
          //   textAlign: TextAlign.start,
          //   style: widget.isFromSinglePost
          //       ? AppTextStyle.contentText1(textColor: AppColors.appBlack)
          //       : AppTextStyle.contentText0(textColor: AppColors.appBlack),
          //   lessStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          //   moreStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          // ),
          ),
    );
  }

  //endregion

  //region Rating Display
  Widget ratingDisplay() {
    // Check if rating is available and not null/empty
    bool hasRating = widget.postDetail.ratingCount != null &&
        widget.postDetail.ratingCount!.isNotEmpty &&
        widget.postDetail.ratingCount != "0" &&
        widget.postDetail.ratingCount != "null";

    if (!hasRating) {
      return const SizedBox();
    }

    double rating = 0.0;
    try {
      rating = double.parse(widget.postDetail.ratingCount!);
    } catch (e) {
      return const SizedBox();
    }

    return Container(
      alignment: Alignment.centerLeft,
      margin: const EdgeInsets.only(left: 15, right: 15, bottom: 5),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          RatingBarIndicator(
            rating: rating,
            itemBuilder: (context, index) => SvgPicture.asset(
              AppImages.star,
              // height: 12,
              // width: 12,
            ),
            itemCount: 5,
            itemSize: 24.0,
            direction: Axis.horizontal,
            unratedColor: AppColors.textFieldFill1,
          ),
          const SizedBox(width: 8),
          Text(
            _getRatingLabel(rating),
            style: AppTextStyle.access0(textColor: AppColors.appBlack),
          ),
        ],
      ),
    );
  }

  //endregion

  String _getRatingLabel(double rating) {
    if (rating >= 4.5) {
      return '•  Awesome!!';
    } else if (rating >= 4.0) {
      return '•  Really Good!';
    } else if (rating >= 3.0) {
      return '•  Decent';
    } else if (rating >= 2.0) {
      return '•  OKish';
    } else {
      return '•  Disappointing';
    }
  }

  //region Reviewed Product Section
  Widget reviewedProductSection() {
    // Only show for review posts that have reviewed products
    if (widget.postDetail.reviewedProducts == null ||
        widget.postDetail.reviewedProducts!.isEmpty ||
        (widget.postDetail.commentType != 'REVIEW' &&
            widget.postDetail.commentType != 'EXTERNAL_REVIEW' &&
            widget.postDetail.contentHeaderText?.contains('review') != true)) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(left: 15, right: 15, bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // "Reviewed product" header with dropdown arrow
          GestureDetector(
            onTap: () {
              setState(() {
                _isReviewedProductExpanded = !_isReviewedProductExpanded;
              });
            },
            child: Row(
              children: [
                Text(
                    widget.postDetail.reviewedProducts?.isNotEmpty == true &&
                            widget.postDetail.reviewedProducts!.first.type
                                    ?.toLowerCase() ==
                                'store'
                        ? 'Reviewed store'
                        : 'Reviewed product',
                    style: AppTextStyle.access0(textColor: AppColors.appBlack)),
                const SizedBox(width: 4),
                AnimatedRotation(
                  turns: _isReviewedProductExpanded ? 0.5 : 0.0,
                  duration: const Duration(milliseconds: 200),
                  child: RotatedBox(
                      quarterTurns: 0,
                      child: SvgPicture.asset(
                        AppImages.arrow,
                        color: AppColors.appBlack,
                        // height: 20,
                        // width: 20,
                      )),
                ),
              ],
            ),
          ),
          // Animated collapsible content
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isReviewedProductExpanded ? 188 : 0, // 180 + 8 for spacing
            child: _isReviewedProductExpanded
                ? Column(
                    children: [
                      const SizedBox(height: 8),
                      // Product card similar to tagged items
                      SizedBox(
                        height: 180,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: widget.postDetail.reviewedProducts!.length,
                          itemBuilder: (context, index) {
                            final product =
                                widget.postDetail.reviewedProducts![index];
                            return GestureDetector(
                              onTap: () {
                                // Navigate to product page
                                if (product.reference != null &&
                                    product.reference!.isNotEmpty) {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          BuyerViewSingleProductScreen(
                                        productReference: product.reference!,
                                      ),
                                    ),
                                  );
                                }
                              },
                              child: Container(
                                width: 125,
                                margin: const EdgeInsets.only(right: 16),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                    color: AppColors.borderColor1,
                                    width: 1.0,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    // Product image
                                    SizedBox(
                                      height: 125,
                                      child: ClipRRect(
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(5),
                                          topRight: Radius.circular(5),
                                        ),
                                        child: extendedImage(
                                          product.icon,
                                          context,
                                          125,
                                          125,
                                          customPlaceHolder:
                                              AppImages.productPlaceHolder,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    // Product details
                                    Container(
                                      height: 50,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 3, vertical: 2),
                                      decoration: const BoxDecoration(
                                        color: AppColors.appWhite,
                                        borderRadius: BorderRadius.only(
                                          bottomLeft: Radius.circular(5),
                                          bottomRight: Radius.circular(5),
                                        ),
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if (product.name?.isNotEmpty ==
                                              true) ...[
                                            Text(
                                              product.name!,
                                              style:
                                                  AppTextStyle.smallTextRegular(
                                                textColor: AppColors.appBlack,
                                              ).copyWith(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                height: 1.2,
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            const SizedBox(height: 2),
                                          ],
                                          if (product.handle?.isNotEmpty ==
                                              true)
                                            Text(
                                              product.handle!,
                                              style:
                                                  AppTextStyle.smallTextRegular(
                                                textColor: AppColors.appBlack,
                                              ).copyWith(
                                                fontSize: 10,
                                                fontWeight: FontWeight.w400,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Counts
  Widget counts() {
    return Visibility(
      visible: widget.postDetail.likeCount != 0 ||
          widget.postDetail.commentCount != 0 ||
          (widget.postDetail.analyticsViewCount != null &&
              widget.postDetail.analyticsViewCount! > 0),
      child: Container(
        margin: const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 0),
        child: Row(
          children: [
            Visibility(
              visible: widget.postDetail.likeCount != 0,
              child: InkWell(
                onTap: () {
                  postCardBloc.goToLikedUsedOrStoreScreen(
                      postReference: widget.postDetail.postOrCommentReference!);
                },
                child: Container(
                    margin: const EdgeInsets.only(right: 5),
                    child: Text(
                      "${widget.postDetail.likeCount} ${widget.postDetail.likeCount == 1 ? "like" : "likes"}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0),
                    )),
              ),
            ),
            Visibility(
              visible: widget.postDetail.commentCount != 0,
              child: Container(
                  margin: const EdgeInsets.only(right: 5),
                  child: Text(
                      "${widget.postDetail.commentCount} ${widget.postDetail.commentCount == 1 ? "comment" : "comments"}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0))),
            ),
            const Expanded(child: SizedBox()),
            Visibility(
              visible: widget.postDetail.analyticsViewCount != null &&
                  widget.postDetail.analyticsViewCount! > 0,
              child: Container(
                  margin: const EdgeInsets.only(right: 0),
                  child: Text(
                      "${widget.postDetail.analyticsViewCount} ${widget.postDetail.analyticsViewCount == 1 ? "view" : "views"}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0))),
            ),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Action
  Widget action() {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          //Like
          VisibilityDetector(
            key: UniqueKey(),
            onVisibilityChanged: (visibilityInfo) {
              var visiblePercentage = visibilityInfo.visibleFraction * 100;
              // If visibility is 100% and is visited is false
              if (visiblePercentage == 100 &&
                  !postCardBloc.postDetail.isVisited) {
                //postCardBloc.visited();
              }
            },
            child: Container(
              margin: const EdgeInsets.only(right: 10),
              height: 26,
              width: 26,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () async {
                  widget.onTapHeart();
                },
                child: SvgPicture.asset(
                  fit: BoxFit.fill,
                  widget.postDetail.likeStatus!
                      ? AppImages.postLike
                      : AppImages.postDisLike,
                  color: widget.postDetail.likeStatus!
                      ? AppColors.red
                      : AppColors.appBlack,
                ),
              ),
            ),
          ),

          //Comment
          Visibility(
            visible: !widget.isFullView!,
            child: Container(
              margin: const EdgeInsets.only(right: 10),
              height: 26,
              width: 26,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  widget.onTapPost();
                },
                child: SvgPicture.asset(AppImages.postComment,
                    color: AppColors.appBlack),
              ),
            ),
          ),
          //Repost
          InkWell(
            onTap: () {
              postCardBloc.rePost(postDetail: widget.postDetail);
            },
            child: SizedBox(
              height: 26,
              width: 26,
              child: SvgPicture.asset(
                AppImages.repost,
                color:
                    widget.postDetail.contentCategory == EntityType.REPOST.name
                        ? AppColors.brandGreen
                        : AppColors.appBlack,
              ),
            ),
          ),
          const Expanded(child: SizedBox()),

          //Share
          SizedBox(
            height: 26,
            width: 26,
            child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  widget.onTapShare();
                },
                child: SvgPicture.asset(
                  AppImages.sharePost,
                  color: AppColors.appBlack,
                )),
          ),
          const SizedBox(
            width: 10,
          ),
          //Save
          InkWell(
            onTap: () {
              postCardBloc.savePost(postDetail: widget.postDetail);
            },
            child: widget.postDetail.saveStatus!
                ? Container(
                    padding:
                        EdgeInsets.only(left: 4, right: 4, top: 2, bottom: 4),
                    height: 26,
                    width: 26,
                    child: SvgPicture.asset(AppImages.savePostActive,
                        color: AppColors.appBlack))
                : SizedBox(
                    height: 26,
                    width: 26,
                    child: SvgPicture.asset(AppImages.savePost,
                        color: AppColors.appBlack)),
          ),
        ],
      ),
    );
  }
//endregion

//region Simple post heading
  Widget simplePostHeader() {
    return PostAndProductAppBar(
      verifiedWidget: VerifiedBadge(
        width: 15,
        height: 15,
        subscriptionType: widget.postDetail.createdBy!.subscriptionType,
      ),
      onTapProfileImage: () {
        widget.onTapProfileImage();
      },
      icon: widget.postDetail.createdBy!.icon,
      title: widget.postDetail.createdBy!.handle!,
      subTitle: postCardBloc.convertDateFormat(
          inputDateTimeString: widget.postDetail.createdDate!),
      entityType: widget.postDetail.createdBy!.entityType!,
      level: widget.postDetail.createdBy!.level.toString(),
      onTapOptions: () {
        postCardBloc.onTapDrawer(postDetail: widget.postDetail);
      },
    );
  }
//endregion

//region Reposted post heading
  Widget repostedPostHeader() {
    // Check if a subtitle (customTitle) is present
    bool hasSubtitle =
        widget.customTitle != null && widget.customTitle!.isNotEmpty;

    return PostAndProductAppBar(
      onTapProfileImage: () {
        widget.onTapProfileImage();
      },
      icon: widget.postDetail.createdBy!.icon,
      title: "",
      level: widget.postDetail.createdBy!.level.toString(),
      customTitle: hasSubtitle
          // If subtitle is present, show date beside handle
          ? Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.postDetail.createdBy!.handle!,
                  style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)
                      .copyWith(height: 0),
                  overflow: TextOverflow.ellipsis,
                ),
                VerifiedBadge(
                  width: 15,
                  height: 15,
                  subscriptionType:
                      widget.postDetail.createdBy!.subscriptionType,
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  postCardBloc.convertDateFormat(
                      inputDateTimeString: widget.postDetail.createdDate!),
                  overflow: TextOverflow.ellipsis,
                  style:
                      AppTextStyle.smallText(textColor: AppColors.writingBlack1)
                          .copyWith(height: 0),
                )
              ],
            )
          // If no subtitle, only show handle (date will be shown below)
          : Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.postDetail.createdBy!.handle!,
                  style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)
                      .copyWith(height: 0),
                  overflow: TextOverflow.ellipsis,
                ),
                VerifiedBadge(
                  width: 15,
                  height: 15,
                  subscriptionType:
                      widget.postDetail.createdBy!.subscriptionType,
                ),
              ],
            ),
      // If no subtitle, show date as subtitle
      subTitle: !hasSubtitle
          ? postCardBloc.convertDateFormat(
              inputDateTimeString: widget.postDetail.createdDate!)
          : "",
      customSubTitle: hasSubtitle
          ? ReadMoreText(
              '${widget.customTitle}',
              trimMode: TrimMode.Line,
              trimLines: widget.isFullView ?? false ? 1000 : 3000,
              colorClickableText: Colors.pink,
              style: widget.isFromSinglePost
                  ? AppTextStyle.subTitle(textColor: AppColors.appBlack)
                      .copyWith(fontSize: 12)
                      .copyWith(height: 0)
                  : AppTextStyle.subTitle(textColor: AppColors.appBlack)
                      .copyWith(fontSize: 12)
                      .copyWith(height: 0),
              lessStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1)
                      .copyWith(height: 0),
              moreStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1)
                      .copyWith(height: 0),
              trimLength: 100000,
              trimCollapsedText: "",
              trimExpandedText: "",
              textAlign: TextAlign.start,
              annotations: [
                Annotation(
                  regExp: RegExp(r"\{\{mention:\{[^}]*\}\}\}"),
                  spanBuilder: ({required String text, TextStyle? textStyle}) {
                    // Extract display text from encoded mention
                    String displayText = MentionParser.extractDisplayText(text);

                    return TextSpan(
                      text: displayText,
                      style:
                          AppTextStyle.access0(textColor: AppColors.brandGreen)
                              .copyWith(fontSize: 12, height: 0),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          // Use the display text for navigation
                          OnTapTag(context, displayText);
                        },
                    );
                  },
                ),
              ],
            )
          : null,
      entityType: widget.postDetail.createdBy!.entityType!,
      onTapOptions: () {
        postCardBloc.onTapDrawer(postDetail: widget.postDetail);
      },
    );
  }
//endregion

//region Review post heading
  Widget reviewPostHeader() {
    return PostAndProductAppBar(
      verifiedWidget: VerifiedBadge(
        width: 15,
        height: 15,
        subscriptionType: widget.postDetail.createdBy!.subscriptionType,
      ),
      onTapProfileImage: () {
        widget.onTapProfileImage();
      },
      icon: widget.postDetail.createdBy!.icon,
      title: widget.postDetail.createdBy!.handle!,
      subTitle: postCardBloc.convertDateFormat(
          inputDateTimeString: widget.postDetail.createdDate!),
      customSubTitle: widget.postDetail.contentHeaderText != null
          ? GestureDetector(
              onTap: () {
                // Navigate to the reviewed product when header is tapped
                _navigateToReviewedProduct();
              },
              child: Text(
                widget.postDetail.contentHeaderText!,
                style: AppTextStyle.smallText(textColor: AppColors.appBlack)
                    .copyWith(fontSize: 12, height: 1.2),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            )
          : null,
      entityType: widget.postDetail.createdBy!.entityType!,
      level: widget.postDetail.createdBy!.level.toString(),
      onTapOptions: () {
        postCardBloc.onTapDrawer(postDetail: widget.postDetail);
      },
    );
  }
//endregion

//region Navigate to Reviewed Product
  void _navigateToReviewedProduct() {
    // Get the first reviewed product reference if available
    if (widget.postDetail.reviewedProducts != null &&
        widget.postDetail.reviewedProducts!.isNotEmpty) {
      final productReference =
          widget.postDetail.reviewedProducts!.first.reference;
      if (productReference != null && productReference.isNotEmpty) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BuyerViewSingleProductScreen(
              productReference: productReference,
            ),
          ),
        );
        return;
      }
    }

    // Fallback: try to extract product reference from mainParentId
    // (since review comments often have the product reference as mainParentId)
    if (widget.postDetail.mainParentId != null &&
        widget.postDetail.mainParentId!.isNotEmpty &&
        widget.postDetail.mainParentId!.startsWith('P')) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BuyerViewSingleProductScreen(
            productReference: widget.postDetail.mainParentId!,
          ),
        ),
      );
      return;
    }

    // If no product reference found, show a message
    CommonMethods.toastMessage("Product not found", context);
  }
//endregion

//region Tagged Items Dropdown
  bool _showTaggedItems = false;
  final Map<String, List<SuggestionItem>> _taggedItems = {};
  String _selectedTabType = ''; // Track which tab is selected
  bool _isLoadingTaggedItems = false;
  bool _hasLoadedTaggedItems = false; // Track if we've already loaded the data
  bool _isReviewedProductExpanded =
      false; // Track reviewed product section state

  Future<void> _loadTaggedItemsForTab(String tabType) async {
    // If we've already loaded the data, just switch tabs locally
    if (_hasLoadedTaggedItems) {
      setState(() {
        _selectedTabType = tabType;
      });
      debugPrint('=== Switching Tab Locally ===');
      debugPrint('Switched to tab: $tabType');
      debugPrint('Items for $tabType: ${_taggedItems[tabType]?.length ?? 0}');
      return;
    }

    // Prevent multiple simultaneous API calls
    if (_isLoadingTaggedItems) return;

    setState(() {
      _isLoadingTaggedItems = true;
    });

    try {
      final response = await PostService().getPostTaggedObjectDetails(
        postReference: widget.postDetail.postOrCommentReference!,
      );

      if (response.data != null && response.data!.isNotEmpty && mounted) {
        setState(() {
          // Clear any existing data to prevent duplicates
          _taggedItems.clear();

          // Get all tagged objects from the first (and only) PostTaggedObjectsData
          var allTaggedObjects = response.data!.first.getAllTaggedObjects();

          // Add products
          if (allTaggedObjects['products'] != null) {
            _taggedItems['PRODUCT'] = [];
            for (var product in allTaggedObjects['products']!) {
              _taggedItems['PRODUCT']!.add(
                SuggestionItem(
                  reference: product.reference!,
                  primaryText: product.handle!,
                  secondaryText: product.name ?? '',
                  imageUrl: product.icon ?? '',
                  type: 'PRODUCT',
                ),
              );
            }
          }

          // Add stores
          if (allTaggedObjects['stores'] != null) {
            _taggedItems['STORE'] = [];
            for (var store in allTaggedObjects['stores']!) {
              _taggedItems['STORE']!.add(
                SuggestionItem(
                  reference: store.reference!,
                  primaryText: store.handle!,
                  secondaryText: store.name ?? '',
                  imageUrl: store.icon ?? '',
                  type: 'STORE',
                ),
              );
            }
          }

          // Add users
          if (allTaggedObjects['users'] != null) {
            _taggedItems['USER'] = [];
            for (var user in allTaggedObjects['users']!) {
              _taggedItems['USER']!.add(
                SuggestionItem(
                  reference: user.reference!,
                  primaryText: user.handle!,
                  secondaryText: user.name ?? '',
                  imageUrl: user.icon ?? '',
                  type: 'USER',
                ),
              );
            }
          }

          _selectedTabType = tabType;
          _hasLoadedTaggedItems = true; // Mark as loaded

          debugPrint('=== API Call Completed (First Time) ===');
          debugPrint('Loaded ALL tagged items for post');
          debugPrint('Products: ${_taggedItems['PRODUCT']?.length ?? 0}');
          debugPrint('Stores: ${_taggedItems['STORE']?.length ?? 0}');
          debugPrint('Users: ${_taggedItems['USER']?.length ?? 0}');
          debugPrint('Selected tab type: $_selectedTabType');
        });
      }
    } catch (e) {
      debugPrint('=== API Call Failed ===');
      debugPrint('Error: $e');
      if (mounted) {
        CommonMethods.toastMessage("Failed to load tagged items", context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingTaggedItems = false;
        });
      }
    }
  }

  Widget taggedItemsDropdown() {
    // Don't show anything if there are no tagged items
    if ((widget.postDetail.taggedProductsCount ?? 0) == 0 &&
        (widget.postDetail.taggedStoresCount ?? 0) == 0 &&
        (widget.postDetail.taggedUsersCount ?? 0) == 0) {
      return const SizedBox.shrink();
    }

    // Create a list of dummy items to show counts only (no API call yet)
    List<SuggestionItem> dummyItems = [];

    // Add dummy products based on count
    for (int i = 0; i < (widget.postDetail.taggedProductsCount ?? 0); i++) {
      dummyItems.add(SuggestionItem(type: 'PRODUCT', reference: 'dummy_$i'));
    }

    // Add dummy stores based on count
    for (int i = 0; i < (widget.postDetail.taggedStoresCount ?? 0); i++) {
      dummyItems
          .add(SuggestionItem(type: 'STORE', reference: 'dummy_store_$i'));
    }

    // Add dummy users based on count
    for (int i = 0; i < (widget.postDetail.taggedUsersCount ?? 0); i++) {
      dummyItems.add(SuggestionItem(type: 'USER', reference: 'dummy_user_$i'));
    }

    // Show the tagged items widget
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
      child: _isLoadingTaggedItems
          ? const Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          : TaggedItemsWidget(
              key: ValueKey(
                  '${_taggedItems.length}_$_selectedTabType'), // Force rebuild when data changes
              taggedItems: _taggedItems.isNotEmpty
                  ? _taggedItems.values.expand((x) => x).toList()
                  : dummyItems, // Use dummy items to show counts only
              showRemoveButton: false,
              showTabs: true,
              showFullPreviewByDefault: false, // Collapsed view for post cards
              selectedTab: _selectedTabType,
              onTabTapped: (tabType) {
                // Load tagged items (API call only on first time, then local switching)
                debugPrint('=== Post Card Tab Tapped ===');
                debugPrint('Tab type: $tabType');
                debugPrint('Has loaded data: $_hasLoadedTaggedItems');
                debugPrint('Current _taggedItems keys: ${_taggedItems.keys}');
                debugPrint('Current _selectedTabType: $_selectedTabType');
                _loadTaggedItemsForTab(tabType);
              },
              onTabChanged: (tabType) {
                setState(() {
                  _selectedTabType = tabType;
                });
              },
            ),
    );
  }
//endregion
}
