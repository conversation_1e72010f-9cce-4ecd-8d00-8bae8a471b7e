import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreCreationAuthService {
  // region Variables
  final HttpService httpService = HttpService();
  // endregion

  // region Send Store Creation OTP
  Future<Map<String, dynamic>> sendStoreCreationOtp({
    required String userReference,
    required String phoneNumber,
  }) async {
    // Create request body
    var body = {
      "user_reference": userReference,
      "phone_number": phoneNumber,
    };

    // Log the request for debugging
    debugPrint("Sending store creation OTP request: $body");

    // Define API endpoint
    String url = "${AppConstants.baseUrl}/store/send_store_creation_otp/";

    // Make the API call
    Map<String, dynamic> response = await httpService.postApiCall(body, url);

    // Log the response for debugging
    debugPrint("Send store creation OTP response: $response");

    return response;
  }
  // endregion

  // region Verify Store Creation OTP
  Future<Map<String, dynamic>> verifyStoreCreationOtp({
    required String userReference,
    required String phoneNumber,
    required String otp,
  }) async {
    // Create request body
    var body = {
      "user_reference": userReference,
      "phone_number": phoneNumber,
      "otp": otp,
    };

    // Log the request for debugging
    debugPrint("Verifying store creation OTP request: $body");

    // Define API endpoint
    String url = "${AppConstants.baseUrl}/store/verify_store_creation_otp/";

    // Make the API call
    Map<String, dynamic> response = await httpService.postApiCall(body, url);

    // Log the response for debugging
    debugPrint("Verify store creation OTP response: $response");

    return response;
  }
  // endregion

  // region Check Phone Verification Status
  Future<bool> isPhoneVerified({required String userReference}) async {
    // First, try to check the local user model
    try {
      // Get the current app context
      final context = AppConstants.userStoreCommonBottomNavigationContext;

      // Get the LoggedInUserInfoDataModel
      final loggedInUserInfoDataModel =
          Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

      // Check if the user detail exists and if the phone is verified
      if (loggedInUserInfoDataModel.userDetail != null) {
        bool isVerified =
            loggedInUserInfoDataModel.userDetail!.isPhonenumberVerified ??
                false;
        bool hasPhoneNumber =
            loggedInUserInfoDataModel.userDetail!.phonenumber != null &&
                loggedInUserInfoDataModel.userDetail!.phonenumber!.isNotEmpty;

        debugPrint(
            "Local phone verification check - isVerified: $isVerified, hasPhoneNumber: $hasPhoneNumber");
        debugPrint(
            "Phone number: ${loggedInUserInfoDataModel.userDetail!.phonenumber}");

        // If the phone is verified and there is a phone number, return true
        if (isVerified && hasPhoneNumber) {
          return true;
        }
      }
    } catch (e) {
      debugPrint("Error checking local phone verification status: $e");
      // Continue to API check if local check fails
    }

    // If we couldn't check locally or the phone isn't verified, try the API
    try {
      String url =
          "${AppConstants.baseUrl}/user/check_phone_verification_status/$userReference/";

      // Make the API call
      Map<String, dynamic> response = await httpService.getApiCall(url);

      // Return the verification status
      bool isVerified = response['is_phone_verified'] ?? false;
      debugPrint("API phone verification check - isVerified: $isVerified");
      return isVerified;
    } catch (e) {
      // If there's an error with the API call, log it and return false
      debugPrint("Error checking phone verification status via API: $e");
      return false;
    }
  }
  // endregion
}
