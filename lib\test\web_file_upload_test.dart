import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/util/web_file_picker.dart';

/// A simple test widget to verify web file upload functionality
class WebFileUploadTest extends StatefulWidget {
  const WebFileUploadTest({Key? key}) : super(key: key);

  @override
  State<WebFileUploadTest> createState() => _WebFileUploadTestState();
}

class _WebFileUploadTestState extends State<WebFileUploadTest> {
  Uint8List? _imageBytes;
  String? _imageName;
  String _status = 'No file selected';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Web File Upload Test'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_imageBytes != null)
              Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                ),
                child: Image.memory(
                  _imageBytes!,
                  fit: BoxFit.cover,
                ),
              )
            else
              Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                ),
                child: const Center(
                  child: Text('No image selected'),
                ),
              ),
            const SizedBox(height: 20),
            Text('File name: ${_imageName ?? 'None'}'),
            const SizedBox(height: 20),
            Text('Status: $_status'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _pickImage,
              child: const Text('Pick Image'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    if (!kIsWeb) {
      setState(() {
        _status = 'This test is for web platform only';
      });
      return;
    }

    try {
      final result = await WebFilePicker.pickImage();
      if (result != null) {
        setState(() {
          _imageBytes = result['bytes'] as Uint8List;
          _imageName = result['name'] as String;
          _status = 'Image selected successfully';
        });
      } else {
        setState(() {
          _status = 'No image selected';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }
}
