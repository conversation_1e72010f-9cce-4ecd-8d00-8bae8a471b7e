import 'package:app_links/app_links.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/universal_link/comment_link.dart';
import 'package:swadesic/util/universal_link/external_review_link.dart';
import 'package:swadesic/util/universal_link/post_link.dart';
import 'package:swadesic/util/universal_link/product_link.dart';
import 'package:swadesic/util/universal_link/store_and_user_link.dart';
import 'package:swadesic/util/universal_link/store_external_review_link.dart';

class HandleUrl {
  //region Constructor
  HandleUrl() {
    //If web
    if (kIsWeb) {
      //If web
      if (AppConstants.webChangedUrl.isNotEmpty &&
          Uri.parse(AppConstants.webChangedUrl).pathSegments.isNotEmpty) {
        debugPrint(
            "In HandleUrl we get the incoming link: ${AppConstants.webChangedUrl}");
        handleAndNavigate(url: Uri.parse(AppConstants.webChangedUrl));
      }
    } else {
      //Get app data
      urlListener();
    }
  }
  //endregion

  //region Url listener
  /// Initializes the `AppLinks` instance and listens for app link events.
  ///
  /// This method is responsible for setting up the necessary infrastructure to handle app link navigation.
  /// It creates an instance of `AppLinks` and listens for any incoming app link events using the `allUriLinkStream`.
  /// When an app link event is received, the method calls the `handleAndNavigate()` function to process the link and navigate the user accordingly.
  void urlListener() async {
    //App link initialize
    final appLinks = AppLinks();

    ///If app is not opened then fetch the latest app link
    if (!AppConstants.isAppOpen) {
      //If Latest app link
      Uri? url = await appLinks.getLatestAppLink();
      //If url is not null then process the url
      if (url != null && url.pathSegments.isNotEmpty) {
        debugPrint("In HandleUrl we get the incoming link is ${url}");
        await handleAndNavigate(url: url);
      }
    }
    //Listen app link
    appLinks.uriLinkStream.listen((link) async {
      ///If app already open then fetch the link from the listener
      if (AppConstants.isAppOpen) {
        Uri? uri = link;
        debugPrint("In HandleUrl we get the incoming link is ${uri}");
        await handleAndNavigate(url: uri);
      }
    });
    //Mark that app is opened
    AppConstants.isAppOpen = true;
  }
  //endregion

  ///Handle web app url
  //region Handle app url
  Future<void> handleAndNavigate({required Uri url}) async {
    Uri completeDecodedUrl = Uri();

    //Decode url path segment if the path segment is encoded and not empty
    if (url.pathSegments.isNotEmpty &&
        CommonMethods.canDecodeBase32(url.pathSegments.first)) {
      completeDecodedUrl = Uri.parse(
          "https://xyz.com/${CommonMethods.decodeBase32(url.pathSegments.first)}");
    } else {
      completeDecodedUrl = url;
    }

    try {
      await Future.delayed(const Duration(seconds: 1));

      ///0. Check is it a web url
      if (url.toString().contains("/web")) {
        //print(url.toString());
        CommonMethods.opeAppWebView(
            webUrl: url.toString(),
            context: AppConstants.userStoreCommonBottomNavigationContext!);
        return;
      }

      ///0.1. Check if it's an FAQ URL (check original URL first, not decoded)
      if (url.pathSegments.isNotEmpty && url.pathSegments.first == 'faq') {
        FaqNavigation.handleFaqLink(
          AppConstants.userStoreCommonBottomNavigationContext!,
          url, // Use original URL, not decoded
        );
        return;
      }

      //Check if the url has 'ic' parameter (both original and decoded URLs)
      if (url.queryParameters.containsKey('ic') &&
          url.queryParameters['ic'] != null) {
        //Save referral code from original URL
        AppDataService()
            .saveReferralCode(referralCode: url.queryParameters['ic']!);
      } else if (completeDecodedUrl.queryParameters.containsKey('ic') &&
          completeDecodedUrl.queryParameters['ic'] != null) {
        //Save referral code from decoded URL
        AppDataService().saveReferralCode(
            referralCode: completeDecodedUrl.queryParameters['ic']!);
      }

      ///1. Handle new URL format: Store handle with product slug
      /// Format: https://lol.swadesic.com/kitten_kreeps/product/kitten-computer
      if (url.pathSegments.length == 3 &&
          url.pathSegments[1] == 'product' &&
          url.pathSegments[2].isNotEmpty) {
        ProductSlugLink(url.pathSegments[0], url.pathSegments[2].split('&')[0]);
        return;
      }

      ///2. Handle new URL format: Store handle with external review request
      /// Format: https://lol.swadesic.com/kitten_kreeps/e-review-request/TOKEN
      if (url.pathSegments.length == 3 &&
          url.pathSegments[1] == 'e-review-request' &&
          url.pathSegments[2].isNotEmpty) {
        String token = url.pathSegments[2];
        // For external review links, we need to decode the token to get the parameters
        try {
          String decodedToken = CommonMethods.decodeBase32(token);
          Uri tokenUri = Uri.parse("https://xyz.com$decodedToken");

          // Check for product external review request (has 'pr' parameter)
          if (tokenUri.queryParameters.containsKey('t') &&
              tokenUri.queryParameters.containsKey('pr') &&
              tokenUri.queryParameters.containsKey('ur')) {
            String actualToken = tokenUri.queryParameters['t']!;
            String productReference = tokenUri.queryParameters['pr']!;
            String userReference = tokenUri.queryParameters['ur']!;

            ExternalReviewLink(actualToken,
                productReference: productReference,
                userReference: userReference);
            return;
          }
          // Check for store external review request (has 'sr' parameter)
          else if (tokenUri.queryParameters.containsKey('t') &&
              tokenUri.queryParameters.containsKey('sr') &&
              tokenUri.queryParameters.containsKey('ur')) {
            String actualToken = tokenUri.queryParameters['t']!;
            String storeReference = tokenUri.queryParameters['sr']!;
            String userReference = tokenUri.queryParameters['ur']!;

            // Handle store external review with dedicated class
            StoreExternalReviewLink(actualToken,
                storeReference: storeReference, userReference: userReference);
            return;
          }
        } catch (e) {
          // If decoding fails, treat as invalid link
          debugPrint("Failed to decode external review token: $e");
        }
      }

      ///3. Handle Base32 encoded URLs (Post, Comment, Reply)
      /// Format: https://lol.swadesic.com/BASE32_ENCODED_STRING
      if (url.pathSegments.length == 1 &&
          CommonMethods.canDecodeBase32(url.pathSegments.first)) {
        String decodedData = CommonMethods.decodeBase32(url.pathSegments.first);
        debugPrint("Decoded data: $decodedData");

        try {
          // Parse the decoded data as a query string to extract parameters
          // The decoded data format is: "d?r=REFERENCE&ic=INVITE_CODE"
          Uri decodedUri = Uri.parse("https://temp.com/$decodedData");

          // Check if it contains the 'r' parameter (reference)
          if (decodedUri.queryParameters.containsKey('r')) {
            String reference = decodedUri.queryParameters['r']!;
            debugPrint("Extracted reference: $reference");

            // Check if it's a post reference (starts with PO)
            if (reference.startsWith('PO')) {
              PostLink(reference);
              return;
            }
            // Check if it's a comment or reply reference (starts with CO)
            // Both comments and replies use CO prefix and are handled by the same screen
            else if (reference.startsWith('CO')) {
              PostLink(
                  reference); // Comments and replies are handled by PostLink/SinglePostViewScreen
              return;
            }
            // Check if it's a product reference (starts with P but not PO)
            else if (reference.startsWith('P') && !reference.startsWith('PO')) {
              ProductLink(reference);
              return;
            }
            // If it's any other type of reference, try to handle it as a post/comment
            else {
              PostLink(reference);
              return;
            }
          }
        } catch (e) {
          debugPrint("Error parsing decoded data as query string: $e");
          // Fallback to old logic for backward compatibility
        }

        // Fallback: Check if decoded data directly starts with reference (old format)
        if (decodedData.startsWith('PO')) {
          PostLink(decodedData);
          return;
        }
        // Check if it's a comment or reply reference (starts with CO)
        else if (decodedData.startsWith('CO')) {
          PostLink(decodedData);
          return;
        }
        // Check if it's a product reference (starts with P but not PO)
        else if (decodedData.startsWith('P') && !decodedData.startsWith('PO')) {
          ProductLink(decodedData);
          return;
        }
        // If it's any other type of reference, try to handle it as a post/comment
        else {
          PostLink(decodedData);
          return;
        }
      }

      ///4. Handle legacy format: Post with query parameter
      if (completeDecodedUrl.queryParameters.containsKey('r') &&
          completeDecodedUrl.queryParameters['r'] != null &&
          completeDecodedUrl.queryParameters['r']!.isNotEmpty &&
          completeDecodedUrl.queryParameters['r']!.startsWith('PO')) {
        PostLink(completeDecodedUrl.queryParameters['r']!);
        return;
      }

      ///5. Handle legacy format: Product with query parameter
      else if (completeDecodedUrl.queryParameters.containsKey('r') &&
          completeDecodedUrl.queryParameters['r'] != null &&
          completeDecodedUrl.queryParameters['r']!.isNotEmpty &&
          completeDecodedUrl.queryParameters['r']!.startsWith('P')) {
        ProductLink(completeDecodedUrl.queryParameters['r']!);
        return;
      }

      ///6. Handle legacy format: External Review (Firebase deep link)
      // Product external review
      else if (completeDecodedUrl.queryParameters.containsKey('pr') &&
          completeDecodedUrl.queryParameters.containsKey('t') &&
          (completeDecodedUrl.queryParameters.containsKey('ur'))) {
        // Get the token from the URL
        String token = completeDecodedUrl.queryParameters['t']!;
        String productReference = completeDecodedUrl.queryParameters['pr']!;
        String userReference = completeDecodedUrl.queryParameters['ur']!;

        // Handle product external review link with the token
        ExternalReviewLink(token,
            productReference: productReference, userReference: userReference);
        return;
      }
      // Store external review
      else if (completeDecodedUrl.queryParameters.containsKey('sr') &&
          completeDecodedUrl.queryParameters.containsKey('t') &&
          (completeDecodedUrl.queryParameters.containsKey('ur'))) {
        // Get the token from the URL
        String token = completeDecodedUrl.queryParameters['t']!;
        String storeReference = completeDecodedUrl.queryParameters['sr']!;
        String userReference = completeDecodedUrl.queryParameters['ur']!;

        // Handle store external review link with the token
        StoreExternalReviewLink(token,
            storeReference: storeReference, userReference: userReference);
        return;
      }

      ///7. Handle new URL format: Store or User handle (with or without query parameters)
      /// Format: https://lol.swadesic.com/kitten_kreeps/?ic=SWSKITTCC
      /// Format: https://lol.swadesic.com/manoj_subramanyam/?ic=SWSKITTCC
      else if (url.pathSegments.isNotEmpty) {
        String handleName = url.pathSegments.first;
        bool isSuperLink =
            url.path.contains('super_link') || url.path.contains('super_link/');

        StoreAndUserLink(
            handleAndUserName: handleName, isSuperLink: isSuperLink);
        return;
      }

      ///8. Fallback: Handle decoded URL store or user
      else {
        StoreAndUserLink(
            handleAndUserName: completeDecodedUrl.pathSegments.first,
            isSuperLink: (completeDecodedUrl.path.contains('super_link') ||
                completeDecodedUrl.path.contains('super_link/')));
      }
    } catch (error) {
      debugPrint("Error handling URL: $error");
    }
  }
//endregion
}
