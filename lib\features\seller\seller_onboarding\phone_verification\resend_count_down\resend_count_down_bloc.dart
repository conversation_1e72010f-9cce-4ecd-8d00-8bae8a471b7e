import 'dart:async';
import 'package:flutter/material.dart';

class ResendCountDownBloc {
  // region Common Methods
  BuildContext context;
  int startTime = 30;
  late Timer countDownTimer;
  // endregion

  //region Controller
  final countDownCtrl = StreamController<int>.broadcast();
  //endregion

  // region | Constructor |
  ResendCountDownBloc(this.context);
  // endregion

  // region Init
  void init() {
    countDown();
  }
  // endregion

  //region Countdown
  void countDown() {
    countDownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      startTime--;
      countDownCtrl.sink.add(startTime);
      
      // If start time is 0 then close it
      if(startTime == 0) {
        countDownTimer.cancel();
        countDownCtrl.sink.add(startTime);
        startTime = 30;
        return;
      }
    });
  }
  //endregion

  //region Dispose
  void dispose() {
    countDownCtrl.close();
    countDownTimer.cancel();
  }
  //endregion
}
