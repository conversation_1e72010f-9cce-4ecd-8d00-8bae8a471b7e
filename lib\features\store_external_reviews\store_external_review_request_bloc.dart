import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/store_external_reviews/store_external_review_request_screen.dart';
import 'package:swadesic/features/store_external_reviews/store_review_submitted_success_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/services/store_external_review_service/store_external_review_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreExternalReviewRequestBloc {
  // region Common Variables
  BuildContext context;
  String token;
  String storeReference;
  String userReference;

  // Store info
  StoreInfo store = StoreInfo();

  // Review data
  TextEditingController reviewController = TextEditingController();
  double rating = 0.0;
  List<String> selectedImages = [];

  // Services
  final StoreExternalReviewService _storeExternalReviewService =
      StoreExternalReviewService();
  final SingleStoreInfoServices _storeService = SingleStoreInfoServices();

  // State controller
  final stateCtrl = StreamController<StoreExternalReviewState>.broadcast();

  // Check if user is static user
  bool get isStaticUser => AppConstants.staticUser == userReference;

  // endregion

  // region | Constructor |
  StoreExternalReviewRequestBloc(
      this.context, this.token, this.storeReference, this.userReference) {
    _loadStoreInfo();
  }
  // endregion

  // region Load store info
  Future<void> _loadStoreInfo() async {
    try {
      stateCtrl.sink.add(StoreExternalReviewState.loading);

      // Get store info
      var response = await _storeService.getSingleStoreInfo(storeReference);

      if (response.data != null) {
        store = response.data!;
        stateCtrl.sink.add(StoreExternalReviewState.success);
      } else {
        stateCtrl.sink.add(StoreExternalReviewState.failed);
      }
    } catch (error) {
      stateCtrl.sink.add(StoreExternalReviewState.failed);
    }
  }
  // endregion

  // region On tap add images
  Future<void> onTapAddImages() async {
    final ImagePicker picker = ImagePicker();

    try {
      final List<XFile> images = await picker.pickMultiImage();

      if (images.isNotEmpty) {
        for (var image in images) {
          selectedImages.add(image.path);
        }
        stateCtrl.sink.add(StoreExternalReviewState.success); // Refresh UI
      }
    } catch (e) {
      context.mounted
          ? CommonMethods.toastMessage("Failed to pick images", context)
          : null;
    }
  }
  // endregion

  // region On tap remove image
  void onTapRemoveImage(int index) {
    if (index >= 0 && index < selectedImages.length) {
      selectedImages.removeAt(index);
      // Trigger UI update
      stateCtrl.sink.add(StoreExternalReviewState.success);
    }
  }
  // endregion

  // region On tap submit review
  Future<void> onTapSubmitReview() async {
    // Validate input
    if (rating == 0) {
      CommonMethods.toastMessage("Please provide a rating", context);
      return;
    }

    if (reviewController.text.trim().isEmpty) {
      CommonMethods.toastMessage("Please write a review", context);
      return;
    }

    try {
      stateCtrl.sink.add(StoreExternalReviewState.loading);

      // Convert image paths to File objects
      List<File>? imageFiles;
      if (selectedImages.isNotEmpty) {
        imageFiles = selectedImages.map((path) => File(path)).toList();
      }

      // Submit the review
      Map<String, dynamic> response =
          await _storeExternalReviewService.addStoreExternalReview(
        storeReference: storeReference,
        commentText: reviewController.text.trim(),
        ratingCount: rating.toInt(),
        token: token,
        entityReference: userReference,
        images: imageFiles,
      );

      if (response['success'] == true) {
        // Navigate to success screen
        _navigateToSuccessScreen();
      } else {
        stateCtrl.sink.add(StoreExternalReviewState.failed);
        CommonMethods.toastMessage(
            response['message'] ?? "Failed to submit review", context);
      }
    } catch (error) {
      stateCtrl.sink.add(StoreExternalReviewState.failed);
      CommonMethods.toastMessage(
          "Failed to submit review. Please try again.", context);
    }
  }
  // endregion

  // region Navigate to success screen
  void _navigateToSuccessScreen() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => StoreReviewSubmittedSuccessScreen(
          storeName: store.storeName ?? 'Store',
          storeHandle: store.storehandle ?? 'storehandle',
        ),
      ),
    );
  }
  // endregion

  // region On tap sign in
  void onTapSignIn() {
    // Set flag to indicate sign in is opened for static user
    AppConstants.isSignInScreenOpenedForStatisUser = true;

    // Navigate to sign in screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MobileNumberOtpScreen(),
      ),
    );
  }
  // endregion

  // region On rating changed
  void onRatingChanged(double value) {
    rating = value;
    stateCtrl.sink.add(StoreExternalReviewState.success); // Refresh UI
  }
  // endregion

  // region Dispose
  void dispose() {
    stateCtrl.close();
    reviewController.dispose();
  }
  // endregion
}
