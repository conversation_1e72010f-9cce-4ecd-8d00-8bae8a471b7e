import 'package:flutter/material.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access_bloc.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_colors.dart';

class WelcomeStoreScreen extends StatelessWidget {
  final String inviteCode;
  final HomeAccessBloc homeAccessBloc;

  const WelcomeStoreScreen({
    Key? key,
    required this.inviteCode,
    required this.homeAccessBloc,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // No app bar, just the body
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Add some space at the top for the close button
                    const SizedBox(height: 30),
                    Text(
                      'Welcome to Swadesic Stores',
                      style: AppTextStyle.exHeading1(
                          textColor: AppColors.appBlack),
                    ),
                    const SizedBox(height: 16),
                    // Text(
                    //   "Imagine a space where every customer isn't just a buyer—they're part of a loyal community.\n"
                    //   "They, \n \t\t\t - Admire your brand story \n \t\t\t - Interact with your products \n \t\t\t - Share genuine feedback, and \n \t\t\t - Naturally promote your store.\n"
                    //   "They actively post about your products in the marketplace, driving increased and repeat sales.",
                    //   style: AppTextStyle.contentHeading0(
                    //       textColor: AppColors.appBlack),
                    // ),
                    Text(
                      "With Swadesic store you run a business & a community at one place and own a digital asset permanently.",
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                    const SizedBox(height: 24),
                    _buildFeatureItem(
                      Icons.local_fire_department,
                      'Built for Swadeshi Entrepreneurs',
                      'List unlimited products and post content. Sell online, offline with your store. Share your unique store link to your social media pages like Instagram, WhatsApp & YouTube and sell even while you sleep.',
                    ),
                    const SizedBox(height: 16),
                    _buildFeatureItem(
                      Icons.groups,
                      'Give New-Age Shopping Experience',
                      'Designed to help you build your business into a brand with customer community & get repeat sales',
                    ),
                    const SizedBox(height: 16),
                    _buildFeatureItem(
                      Icons.trending_up,
                      'Unlimited & Risk-Free Growth',
                      'No upfront fees or subscriptions to run your store. No commissions on sales. Only pay payment gateway fee and a flat handling fee (₹59) on a successful sale. And no handling fee on local orders & orders below ₹500',
                    ),
                    const SizedBox(height: 24),
                    InkWell(
                      onTap: () {
                        CommonMethods.opeAppWebView(
                            webUrl: 'https://swadesic.sociallyx.com/business',
                            context: context);
                      },
                      child: Text(
                        'Learn more about Swadesic Stores',
                        style: AppTextStyle.access0(
                            textColor: AppColors.brandBlack, isUnderline: true),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              CommonMethods.share(
                                  "${AppConstants.appData.isUserView! ? AppStrings.userInviteeMessage : AppStrings.storeInviteeMessage}${AppConstants.domainName}?ref=$inviteCode");
                            },
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              side: const BorderSide(color: AppColors.appBlack),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Invite a Friend',
                              style: AppTextStyle.access0(
                                  textColor: AppColors.appBlack),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => homeAccessBloc.onTapCreateStore(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.appBlack,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Continue',
                              style: AppTextStyle.access0(
                                  textColor: AppColors.appWhite),
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Add some space at the bottom
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            // Close button at the top right
            Positioned(
              top: 10,
              right: 10,
              child: IconButton(
                icon: const Icon(Icons.close, size: 28),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 28, color: AppColors.appBlack),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyle.access1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingBlack),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
