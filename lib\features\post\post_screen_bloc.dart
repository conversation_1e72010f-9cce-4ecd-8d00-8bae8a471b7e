import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/post_pagination.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/store_comment_service/store_comment_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:async/async.dart';
enum PostScreenState { Loading, Success, Failed, Empty }

class PostScreenBloc {
  //region Common variable
  late BuildContext context;
  final String creatorReference;
  late PostPagination postPagination;
  List<dynamic> postList = [];
  int offset = 0;
  final bool isRepost;
  final PostScreenContentType contentType;

  //AsyncMemoizer
  final AsyncMemoizer asyncMemo = AsyncMemoizer();

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final postStateCtrl = StreamController<PostScreenState>.broadcast();
  final ScrollController previousScrollController ;
  late  ScrollController postScreenScrollController = ScrollController() ;

//endregion

  //region Constructor
  PostScreenBloc(this.context, this.creatorReference, this.previousScrollController, this.isRepost, this.contentType);

  //endregion

//region Init
  init() async{
    //Initialize Post pagination
    postPagination = PostPagination(context,this);
    //Get posts
    await getAllUserOrStorePosts();
  }
//endregion



  //region Get all user or store post
  Future<void>getAllUserOrStorePosts({bool isClearPostList = false})async{
    //Update offset to 10
    offset = 0;
    //Clear post list
    postList.clear();
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);

    //Api call based on content type
    if (contentType == PostScreenContentType.storeReviews) {
      // Fetch store reviews/comments ABOUT the store
      try {
        String visitorReference = AppConstants.appData.isUserView!
            ? AppConstants.appData.userReference!
            : AppConstants.appData.storeReference!;

        List<PostDetail> storeComments = await StoreCommentService().getStoreComments(
          entityReference: creatorReference,
          visitorReference: visitorReference,
          limit: 10,
          offset: 0,
        );

        postList = storeComments;
      } catch (e) {
        // Handle error silently
        print('Error fetching store comments: $e');
        postList = [];
      }
    } else {
      // Original logic for user posts
      //If is repost
      if(isRepost){
        postList = await PostService().getStoreOrUserRePosts(creatorReference:creatorReference, limit: 10,offset: offset,context: context );
        //print("Repost count is ${postList.length}");
      }
      //else normal post
      else{
        postList = await PostService().getStoreOrUserPosts(creatorReference:creatorReference, limit: 10,offset: offset );
      }
    }

    //Filter post and product
    for (var postAndProduct in postList) {
      if (postAndProduct is PostDetail) {
        //Add data in post data model feed
        postDataModel.addPostIntoList(postList:[postAndProduct]);
        // //print('Post: ${postAndProduct.postOrCommentReference}');
      }
      else if (postAndProduct is Product) {
        //Add data to product data model
        productDataModel.addProductIntoList(products:[postAndProduct] );
        // //print('Product: ${postAndProduct.productReference}');
      }
    }



    //Clear all post in data model belongs to the exact reference
    // postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == creatorReference);
    // //Add data in post data model
    // postDataModel.addPostIntoList(postList: postList);
    // //If post is empty in data model
    // if(postDataModel.allPostDetailList.where((element) => element.createdBy!.userOrStoreReference! == creatorReference).isEmpty){
    //   return postStateCtrl.sink.add(PostScreenState.Empty);
    // }
    //If postList is empty
    if (postList.isEmpty) {
      return postStateCtrl.sink.add(PostScreenState.Empty);
    }
    //Success
    postStateCtrl.sink.add(PostScreenState.Success);
    return;

    //Success
    // postStateCtrl.sink.add(PostScreenState.Success);
    // return ;

  }
//endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference && AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference && AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Delete post api call
  Future<void> deletePost({required PostDetail postDetail}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //region Try
    try {
      //Api call
      bool isSuccess = await PostService().deletePost(postRefrence: postDetail.postOrCommentReference!);
      //If is success is false then return
      if(!isSuccess){
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
        return;
      }
      //Remove local data
      //Remove post detail from the userOrStoreFeeds
      // postDataModel.userOrStoreFeedsList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Remove post detail from the allPostDetailList
      postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Refresh ui
      postDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion

  //region On tap heart
  Future<void>onTapHeart({required PostDetail postDetail})async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);


      //Update liked count and is liked
      //Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if(postDetail.likeStatus!){
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      }
      else{
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
      //Api call
      await PostService().likePost(postReference: postDetail.postOrCommentReference!, likeStatus: postDetail.likeStatus!);
    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Failed
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion

  //region Repost
  Future<void>rePost({required PostDetail postDetail})async{
    try{
      // Get reference to the PostDataModel
      // var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Logged in user data model
      var loggedInUserInfoDataModel = Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
      //SellerOwnStoreInfoDataModel store data model
      var sellerOwnStoreInfoDataModel = Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
      //If already reposted then return
      if (postDetail.contentHeaders!.any((element) => element.reference == loggedInUserInfoDataModel.userDetail!.userReference || element.reference == sellerOwnStoreInfoDataModel.storeInfo!.storeReference)) {
        postDetail.contentCategory = "POST";

        //Remove that object from the list
        postDetail.contentHeaders!.removeWhere((element) =>
        element.reference == loggedInUserInfoDataModel.userDetail!.userReference ||
            element.reference == sellerOwnStoreInfoDataModel.storeInfo!.storeReference);
         PostService().repost(postReference: postDetail.postOrCommentReference!, repostStatus: false);

      }
      //Else add
      else{
        postDetail.contentCategory = "REPOST";

        //Update liked count and is liked
        //Toggle the like status
        postDetail.contentHeaders!.add(ContentHeaders(reference:
        AppConstants.appData.isUserView!?loggedInUserInfoDataModel.userDetail!.userReference
            :
        sellerOwnStoreInfoDataModel.storeInfo!.storeReference,
          handle:  AppConstants.appData.isUserView!?loggedInUserInfoDataModel.userDetail!.userName
              :
          sellerOwnStoreInfoDataModel.storeInfo!.storehandle,

        ));
         PostService().repost(postReference: postDetail.postOrCommentReference!, repostStatus: false);
      }
      //Remove that object from the postList whose contentCategory is "POST"
      postList.removeWhere((element) => element.contentCategory == EntityType.POST.name);

      //If post list is empty
      if (postList.isEmpty) {
        postStateCtrl.sink.add(PostScreenState.Empty);
      }
      //Success
      postStateCtrl.sink.add(PostScreenState.Success);

    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Failed
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion


  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}){
    Widget screen= EditPostScreen(postDetail: postDetail,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion



  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail})async{
    List<Map<String, dynamic>> accessOptions = [];
    if(postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ?AppConstants.appData.userReference!
            :AppConstants.appData.storeReference!))
    {
      accessOptions =

      [
        //Copy
        {
          'title': AppStrings.copyPostLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(context, AppLinkCreateService().createPostLink(postReference: postDetail.postOrCommentReference!));
          },
        },
        //Edit
        {
          'title': AppStrings.editPost.toLowerCase(),
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);


          },
        },
        //Delete post
        {
          'title': AppStrings.deletePost,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);

          },
        },
        // Add more options if needed
      ];
    }
    else{
      accessOptions = [
        {
          'title': AppStrings.reportThePost,
          'onTap': () {
            Navigator.pop(context);
            // Navigator.pop(context);
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isPostComment: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);

//endregion

          },
        },
      ];

    }

    CommonMethods.accessBottomSheet(screen: ShareAccessBottomSheet(accessOptions: accessOptions), context: context,);

  }
  //endregion

  //region Confirm delete
  Future confirmDelete({required PostDetail postDetail}){
    return CommonMethods.appDialogBox(
        context: context,
        widget:
        OkayAndCancelDialogScreen(onTapSecondButton:(){
          deletePost(postDetail: postDetail);
        },previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",

        )
    );
  }
//endregion


  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService().createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty ? null : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
      ),
      context: context,
    );
    //
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }
  //endregion


  //region Go to single post view
  void goToSinglePostView({required String postReference}){
    var screen = SinglePostViewScreen(postReference: postReference,);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion


//region Dispose
  void dispose() {
    postStateCtrl.close();
  }
//endregion
}
