class PlanDetailsData {
  String? cta;
  String? name;
  String? price;
  List<String>? benefits;
  String? interval;
  String? forWhomPrimary;
  String? forWhomSecondary;

  PlanDetailsData({
    this.cta,
    this.name,
    this.price,
    this.benefits,
    this.interval,
    this.forWhomPrimary,
    this.forWhomSecondary,
  });

  PlanDetailsData.fromJson(Map<String, dynamic> json) {
    cta = json['cta'];
    name = json['name'];
    price = json['price'];
    benefits = json['benefits']?.cast<String>();
    interval = json['interval'];
    forWhomPrimary = json['forWhomPrimary'];
    forWhomSecondary = json['forWhomSecondary'];
  }
}

class PlanDetail {
  String? planName;
  dynamic planPriceMonthly;
  dynamic planPriceYearly;
  String? planYearlyDiscountString;
  String? planDescription;
  String? planStatusString;
  String? planReferenceMonthly;
  String? planReferenceYearly;
  PlanDetailsData? planDetails;
  String? razorpaySubscriptionId;
  bool? isSubscriptionCancelled;
  bool? isActive;

  PlanDetail({
    this.planName,
    this.planPriceMonthly,
    this.planPriceYearly,
    this.planYearlyDiscountString,
    this.planDescription,
    this.planStatusString,
    this.planReferenceMonthly,
    this.planReferenceYearly,
    this.planDetails,
    this.razorpaySubscriptionId,
    this.isSubscriptionCancelled,
    this.isActive,
  });

  PlanDetail.fromJson(Map<String, dynamic> json) {
    planName = json['plan_name'];
    planPriceMonthly = json['plan_price_monthly'];
    planPriceYearly = json['plan_price_yearly'];
    planYearlyDiscountString = json['plan_yearly_discount_string'];
    planDescription = json['plan_description'];
    planStatusString = json['plan_status_string'];
    planReferenceMonthly = json['plan_reference_monthly'];
    isSubscriptionCancelled = json['is_subscription_cancelled'] ?? false;
    planReferenceYearly = json['plan_reference_yearly'];
    planDetails = json['plan_details'] != null ? PlanDetailsData.fromJson(json['plan_details']) : null;
    razorpaySubscriptionId = json['razorpay_subscription_id'];
    isActive = json['is_active'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['plan_name'] = this.planName;
    data['plan_price_monthly'] = this.planPriceMonthly;
    data['plan_price_yearly'] = this.planPriceYearly;
    data['is_subscription_cancelled'] = this.isSubscriptionCancelled;
    data['plan_yearly_discount_string'] = this.planYearlyDiscountString;
    data['plan_description'] = this.planDescription;
    data['plan_status_string'] = this.planStatusString;
    data['plan_reference_monthly'] = this.planReferenceMonthly;
    data['plan_reference_yearly'] = this.planReferenceYearly;
    data['razorpay_subscription_id'] = this.razorpaySubscriptionId;
    data['is_active'] = this.isActive;
    return data;
  }
}
