import 'dart:async';
import 'dart:typed_data';

/// Stub implementation for non-web platforms
class WebFilePickerImpl {
  /// Stub implementation for non-web platforms
  static Future<Map<String, dynamic>?> pickImage() async {
    return null;
  }
  
  /// Stub implementation for non-web platforms
  static Future<List<Map<String, dynamic>>?> pickMultipleImages() async {
    return null;
  }
  
  /// Stub implementation for non-web platforms
  static Future<Map<String, dynamic>?> pickFile({List<String>? allowedExtensions}) async {
    return null;
  }
  
  /// Stub implementation for non-web platforms
  static Future<List<Map<String, dynamic>>?> pickMultipleFiles({List<String>? allowedExtensions}) async {
    return null;
  }
}
