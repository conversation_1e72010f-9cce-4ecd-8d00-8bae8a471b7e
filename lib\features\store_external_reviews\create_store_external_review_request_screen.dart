import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/store_external_reviews/create_store_external_review_request_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';

class CreateStoreExternalReviewRequestScreen extends StatefulWidget {
  final StoreInfo store;
  final String? storeImage;

  const CreateStoreExternalReviewRequestScreen({
    Key? key,
    required this.store,
    this.storeImage,
  }) : super(key: key);

  @override
  _CreateStoreExternalReviewRequestScreenState createState() =>
      _CreateStoreExternalReviewRequestScreenState();
}

class _CreateStoreExternalReviewRequestScreenState
    extends State<CreateStoreExternalReviewRequestScreen> {
  late CreateStoreExternalReviewRequestBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = CreateStoreExternalReviewRequestBloc(
        context, widget.store, widget.storeImage);
    bloc.init();
  }

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        title: 'Create Store Review Request Link',
        isCustomTitle: true,
        customTitleWidget: Text(
          'Create Store Review Request Link',
          style: AppTextStyle.pageHeading2(textColor: AppColors.appBlack),
        ),
        isDefaultMenuVisible: true,
        onTapReport: () {},
        backgroundColor: AppColors.appWhite,
        isMembershipVisible: false,
        isCartVisible: false,
      ),
      body: StreamBuilder<StoreExternalReviewRequestState>(
        stream: bloc.stateCtrl.stream,
        initialData: StoreExternalReviewRequestState.initial,
        builder: (context, snapshot) {
          if (snapshot.data == StoreExternalReviewRequestState.loading) {
            return Center(child: AppCommonWidgets.appCircularProgress());
          }
          return _buildBody();
        },
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoSection(),
            const SizedBox(height: 24),
            Align(
              alignment: Alignment.center,
              child: _buildStorePreview(),
            ),
            const SizedBox(height: 24),
            _buildCustomerIdentificationSection(),
            const SizedBox(height: 32),
            _buildCreateLinkButton(),
            const SizedBox(height: 16),
            _buildFooterText(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bring Trust into your Swadesic store by requesting reviews from your customers who purchased outside Swadesic.',
            style:
                AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: bloc.onTapKnowMore,
            child: Text(
              'Know more about Store External Reviews',
              style: AppTextStyle.smallTextRegular(
                  textColor: AppColors.brandBlack, isUnderline: true),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorePreview() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageContainer(
              width: 170,
              height: 170,
              imageUrl:
                  widget.store.icon != null && widget.store.icon!.isNotEmpty
                      ? widget.store.icon!
                      : null,
              imageType: CustomImageContainerType.store,
            ),
            const SizedBox(height: 12),
            Text(
              'Store review for @${widget.store.storehandle ?? 'storehandle'}',
              style: AppTextStyle.access1(
                textColor: AppColors.appBlack,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerIdentificationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Reviewer Identification (Optional)',
          style: AppTextStyle.contentHeading0(
            textColor: AppColors.appBlack,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Add a layer of security on who can add an external review by requesting them to identify',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
              .copyWith(
            fontSize: 13,
          ),
        ),
        const SizedBox(height: 16),
        _buildIdentifierDropdown(),
        const SizedBox(height: 16),
        _buildIdentifierTextField(),
      ],
    );
  }

  Widget _buildIdentifierDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: bloc.selectedIdentifierType,
          icon: SvgPicture.asset(
            AppImages.arrow,
            color: AppColors.appBlack,
            height: 24,
            width: 24,
          ),
          underline: const SizedBox(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                bloc.selectedIdentifierType = newValue;
              });
            }
          },
          items: <String>['username', 'phone', 'email']
              .map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value.substring(0, 1).toUpperCase() + value.substring(1),
                style:
                    AppTextStyle.hintText(textColor: AppColors.writingBlack1),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildIdentifierTextField() {
    String hintText = 'Enter the username';
    TextInputType keyboardType = TextInputType.text;

    if (bloc.selectedIdentifierType == 'phone') {
      hintText = 'Enter the phone number';
      keyboardType = TextInputType.phone;
    } else if (bloc.selectedIdentifierType == 'email') {
      hintText = 'Enter the email address';
      keyboardType = TextInputType.emailAddress;
    }

    return AppTextFields.allTextField(
      context: context,
      textEditingController: bloc.userIdentifierController,
      hintText: hintText,
      keyboardType: keyboardType,
      textInputAction: TextInputAction.done,
      enabled: true,
      maxEntry: 100,
      onChanged: (value) {},
      onSaved: () {},
    );
  }

  Widget _buildCreateLinkButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: bloc.onTapCreateLink,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Create Store Review Request Link',
          style: AppTextStyle.button1Bold(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildFooterText() {
    return Text(
      'External reviews comes with an expiry, make sure you inform the customer to add promptly.',
      style: AppTextStyle.smallText(textColor: AppColors.appBlack),
      textAlign: TextAlign.center,
    );
  }
}
