import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:swadesic/model/faq/faq_model.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class FaqService {
  // region Common Variables
  late HttpService httpService;
  static const String _cacheKey = 'faq_data_cache';
  static const String _cacheTimestampKey = 'faq_data_cache_timestamp';
  static const int _cacheValidityHours = 24; // Cache validity in hours

  // endregion

  // region Constructor
  FaqService() {
    httpService = HttpService();
  }
  // endregion

  // region Get FAQ Data with Caching
  Future<FaqResponse> getFaqData({bool forceRefresh = false}) async {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        final cachedData = await _getCachedFaqData();
        if (cachedData != null) {
          return cachedData;
        }
      }

      // Fetch from API
      final response = await _fetchFaqDataFromApi();

      // Cache the response
      await _cacheFaqData(response);

      return response;
    } catch (e) {
      // If API fails, try to return cached data as fallback
      final cachedData = await _getCachedFaqData(ignoreExpiry: true);
      if (cachedData != null) {
        return cachedData;
      }
      rethrow;
    }
  }
  // endregion

  // region Fetch FAQ Data from API
  Future<FaqResponse> _fetchFaqDataFromApi() async {
    Map<String, dynamic> response =
        await httpService.getApiCall(AppConstants.faqData);

    return FaqResponse.fromJson(response);
  }
  // endregion

  // region Cache FAQ Data
  Future<void> _cacheFaqData(FaqResponse faqResponse) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(faqResponse.toJson());
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_cacheKey, jsonString);
      await prefs.setInt(_cacheTimestampKey, timestamp);
    } catch (e) {
      // Cache failure shouldn't break the app
      print('Failed to cache FAQ data: $e');
    }
  }
  // endregion

  // region Get Cached FAQ Data
  Future<FaqResponse?> _getCachedFaqData({bool ignoreExpiry = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_cacheKey);
      final timestamp = prefs.getInt(_cacheTimestampKey);

      if (jsonString == null || timestamp == null) {
        return null;
      }

      // Check if cache is still valid (unless ignoring expiry)
      if (!ignoreExpiry) {
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final now = DateTime.now();
        final difference = now.difference(cacheTime);

        if (difference.inHours > _cacheValidityHours) {
          return null; // Cache expired
        }
      }

      final jsonData = json.decode(jsonString) as Map<String, dynamic>;
      return FaqResponse.fromJson(jsonData);
    } catch (e) {
      // Cache read failure shouldn't break the app
      print('Failed to read cached FAQ data: $e');
      return null;
    }
  }
  // endregion

  // region Clear Cache
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
    } catch (e) {
      print('Failed to clear FAQ cache: $e');
    }
  }
  // endregion

  // region Check Cache Status
  Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_cacheTimestampKey);

      if (timestamp == null) {
        return false;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime);

      return difference.inHours <= _cacheValidityHours;
    } catch (e) {
      return false;
    }
  }
  // endregion

  // region Get Cache Info
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_cacheTimestampKey);
      final hasCache = prefs.containsKey(_cacheKey);

      if (timestamp == null || !hasCache) {
        return {
          'hasCache': false,
          'cacheTime': null,
          'isValid': false,
          'hoursOld': null,
        };
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime);
      final isValid = difference.inHours <= _cacheValidityHours;

      return {
        'hasCache': true,
        'cacheTime': cacheTime.toIso8601String(),
        'isValid': isValid,
        'hoursOld': difference.inHours,
      };
    } catch (e) {
      return {
        'hasCache': false,
        'cacheTime': null,
        'isValid': false,
        'hoursOld': null,
        'error': e.toString(),
      };
    }
  }
  // endregion
}
