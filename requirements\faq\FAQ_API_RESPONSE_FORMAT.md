# FAQ API Response Format

## Required JSON Structure

The FAQ system expects the following JSON structure from your API or Firebase Remote Config:

### Complete Response Format
```json
{
  "faq_categories": [
    {
      "id": "general",
      "name": "General",
      "items": [
        {
          "id": "general_trial",
          "question": "Is there a free trial available?",
          "answer": "Yes, you can try us for free for 30 days. If you want, we'll provide you with a free 30-minute onboarding call to get you up and running. Book a call here.",
          "isExpanded": false
        },
        {
          "id": "general_plan_change",
          "question": "Can I change my plan later?",
          "answer": "Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.",
          "isExpanded": false
        }
      ]
    },
    {
      "id": "pricing",
      "name": "Pricing",
      "items": [
        {
          "id": "pricing_cost",
          "question": "How much does it cost?",
          "answer": "Our pricing starts at ₹29/month for the Basic plan. We also offer Professional (₹59/month) and Enterprise (custom pricing) plans.",
          "isExpanded": false
        },
        {
          "id": "pricing_discounts",
          "question": "Do you offer any discounts?",
          "answer": "Yes, we offer a 20% discount for annual billing and special pricing for non-profit organizations and educational institutions.",
          "isExpanded": false
        }
      ]
    }
  ]
}
```

## Field Descriptions

### Root Object
- `faq_categories` (Array): List of FAQ categories

### Category Object
- `id` (String, Required): Unique identifier for the category (used in deep links)
- `name` (String, Required): Display name for the category
- `items` (Array, Required): List of FAQ questions in this category

### FAQ Item Object
- `id` (String, Required): Unique identifier for the question (used in deep links)
- `question` (String, Required): The FAQ question text
- `answer` (String, Required): The FAQ answer text
- `isExpanded` (Boolean, Optional): Whether the question should be expanded by default (defaults to false)

## ID Naming Conventions

### Category IDs
- Use lowercase, snake_case format
- Keep them short and descriptive
- Examples: `general`, `pricing`, `dashboard`, `api`, `billing`, `support`

### Question IDs
- Use format: `{categoryId}_{descriptive_name}`
- Use lowercase, snake_case format
- Examples: `general_trial`, `pricing_cost`, `api_documentation`

## Deep Link URL Examples

Based on the IDs in your response:

### Category Links
- `https://yourapp.com/faq?category=general`
- `https://yourapp.com/faq?category=pricing`
- `https://yourapp.com/faq?category=dashboard`

### Question Links
- `https://yourapp.com/faq?category=general&question=general_trial`
- `https://yourapp.com/faq?category=pricing&question=pricing_cost`
- `https://yourapp.com/faq?category=api&question=api_documentation`

## Implementation Notes

### For API Integration
```dart
// In your API service
Future<List<FaqCategory>> fetchFaqData() async {
  final response = await http.get(Uri.parse('your-api-endpoint'));
  final data = json.decode(response.body);
  
  return (data['faq_categories'] as List)
      .map((category) => FaqCategory.fromJson(category))
      .toList();
}
```

### For Firebase Remote Config
```dart
// In your Remote Config service
Future<List<FaqCategory>> fetchFaqFromRemoteConfig() async {
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.fetchAndActivate();
  
  final faqJson = remoteConfig.getString('faq_data');
  final data = json.decode(faqJson);
  
  return (data['faq_categories'] as List)
      .map((category) => FaqCategory.fromJson(category))
      .toList();
}
```

## Validation Rules

1. **Category IDs must be unique** across all categories
2. **Question IDs must be unique** across all questions (not just within category)
3. **All required fields must be present** and non-empty
4. **IDs should not contain spaces** or special characters except underscore
5. **Answer text can contain HTML** for formatting (if needed)

## Sample Categories for Different Apps

### E-commerce App
```json
{
  "faq_categories": [
    {"id": "orders", "name": "Orders & Shipping"},
    {"id": "returns", "name": "Returns & Refunds"},
    {"id": "payments", "name": "Payments & Billing"},
    {"id": "account", "name": "Account Management"}
  ]
}
```

### SaaS App
```json
{
  "faq_categories": [
    {"id": "getting_started", "name": "Getting Started"},
    {"id": "billing", "name": "Billing & Subscriptions"},
    {"id": "features", "name": "Features & Usage"},
    {"id": "integrations", "name": "Integrations"},
    {"id": "troubleshooting", "name": "Troubleshooting"}
  ]
}
```

## Error Handling

The app will gracefully handle:
- Missing or invalid IDs (will skip deep linking)
- Malformed JSON (will fall back to dummy data)
- Network errors (will show error state with retry option)
- Empty categories or questions (will show appropriate empty state)
