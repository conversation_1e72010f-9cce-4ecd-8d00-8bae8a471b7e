import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/my_plan/my_plan_bloc.dart';
import 'package:swadesic/features/seller/my_plan/my_plan_card.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class MyPlanScreen extends StatefulWidget {
  const MyPlanScreen({super.key});

  @override
  State<MyPlanScreen> createState() => _MyPlanScreenState();
}

class _MyPlanScreenState extends State<MyPlanScreen> {

  //region Bloc
  late MyPlanBloc myPlanBloc;
  //endregion

  //region Init
  @override
  void initState() {
    myPlanBloc = MyPlanBloc(context);
    myPlanBloc.init();
    super.initState();
  }
  //endregion


  @override
  Widget build(BuildContext context) {
    return Scaffold(

      appBar: appBar(),
      body: body(),
    );
  }


  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title:AppStrings.myPlan ,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion


//region Body
Widget body(){

    return StreamBuilder<MyPlanState>(
      stream: myPlanBloc.myPlanStateCtrl.stream,
      initialData: MyPlanState.Loading,
      builder: (context, snapshot) {
        //Loading
        if (snapshot.data == MyPlanState.Loading) {
          return AppCommonWidgets.appCircularProgress();
        }
        //Success
        if (snapshot.data == MyPlanState.Success) {
          return SingleChildScrollView(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 6,),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
              decoration: BoxDecoration(
                color: AppColors.appWhite, // Background color
                border: Border.all(
                  color: AppColors.appBlack, // Black border color
                  width: 1, // Border width
                ),
                borderRadius: BorderRadius.circular(15), // Rounded corners
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    child: title(),
                  ),
                  const SizedBox(height: 15,),


                  ListView.builder(
                      itemCount: myPlanBloc.myPlanResponse.features!.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context,index){
                        return MyPlanCard(
                          title: "${myPlanBloc.myPlanResponse.features![index].title}",
                          subTitle: "${myPlanBloc.myPlanResponse.features![index].subtitle}",
                          description: "${myPlanBloc.myPlanResponse.features![index].description}",
                        );

                  })
                  // storeCreation(),
                  // productListing(),
                  // contentPosting(),
                  // receivingOrders(),
                  // acceptingPayments(),
                  // payouts(),
                  // deliveryReturnRefundProcessing(),
                  // rewards(),

                ],
              ),
            ),
          );
        }

        //Failed
        return AppCommonWidgets.errorWidget(onTap: (){
          myPlanBloc.init();
        });

      }
    );
}
//endregion


//region Title
Widget title() {
  return Row(
    mainAxisAlignment: MainAxisAlignment.start,
    crossAxisAlignment: CrossAxisAlignment.center,
    mainAxisSize: MainAxisSize.min,
    children: [
      Image.asset(AppImages.myPlanCircularPoint,height: 25,),
      const SizedBox(width: 5,),
      FittedBox(child: Text(myPlanBloc.myPlanResponse.planName ?? "", style: AppTextStyle.usernameHeading(textColor: AppColors.appBlack).copyWith(height: 1),)),
    ],
  );
}
//endregion


//region Store creation
Widget storeCreation(){
    return MyPlanCard(
      title: "Store creation",
      subTitle: "Free",
      description: "Create up to 5 stores across various categories. You can also set up test stores to explore and understand store management on Swadesic.",
    );
}
//endregion


//region Product listing
  Widget productListing(){
    return MyPlanCard(
      title: "Product listing",
      subTitle: "Free",
      description: "List an unlimited number of products, and set custom delivery and refund policies for each product or apply store-wide settings.",
    );
  }
//endregion


//region Content posting
  Widget contentPosting(){
    return MyPlanCard(
      title: "Content posting",
      subTitle: "Free",
      description: "Engage customers by sharing brand stories, product behind-the-scenes, announcements, and more to build lasting connections.",
    );
  }
//endregion

//region Receiving Orders
  Widget receivingOrders(){
    return MyPlanCard(
      title: "Receiving Orders",
      subTitle: "Free",
      description: "Accept unlimited orders without any percentage-based commission. A flat Platform fee of ₹50 is charged for orders with a cart value exceeding ₹500, while no fee is applied for orders below ₹500.",
    );
  }
//endregion

// region Accepting Payments
  Widget acceptingPayments(){
    return MyPlanCard(
      title: "Accepting Payments",
      subTitle: "2.36%",
      description: "Payments are securely processed via Razorpay with a 2.36% fee (inclusive of GST). International, Amex, and EMI payments incur an extra 1% to 1.5%, shown in order details.",
    );
  }
//endregion

// region Payouts
  Widget payouts(){
    return MyPlanCard(
      title: "Payouts",
      subTitle: "T+ FW+ 2 days",
      description: "Payouts for successful orders are credited to your store account balance within T+FW+2 days."
          "\n  •  T = Time of product delivery"
          "\n  •  FW = Fulfillment window (2 days) or the Return window, whichever is longer"
          "\n  •  A 2-day grace period is included to ensure there are no transaction conflicts. Once funds are available in your account balance, you can transfer them to your bank account.",
    );
  }
//endregion

//region Delivery, Return & Refund processing
  Widget deliveryReturnRefundProcessing(){
    return MyPlanCard(
      title: "Delivery, Return & Refund processing",
      subTitle: "",
      description: "Delivery, return, and refund processing follow the settings you define during product listing. The store is responsible for managing product deliveries and returns. Swadesic handles refunds, and any associated costs, such as the transaction fees, will be deducted from your store account balance.",
    );
  }
//endregion


//region rewards
  Widget rewards(){
    return MyPlanCard(
      title: "Rewards",
      subTitle: "",
      description: "Swadesic credits ₹500 in Flash points on the first day of every month to support your business in the Swadeshi Movement. Flash points automatically cover the ₹50 platform fee per order.",
    );
  }
//endregion

}
