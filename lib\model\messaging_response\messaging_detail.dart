import 'package:intl/intl.dart';
import 'package:swadesic/model/messaging_response/dm_meta_data_response.dart';

class MessageDetailResponse {
  String? message;
  int? unreadCount;
  String? lastRead;
  String? activeStatusString;
  Data? data;
  DmMetaData? dmMetaData;

  MessageDetailResponse(
      {this.message, this.unreadCount,
        this.activeStatusString,
        this.lastRead, this.data});

  MessageDetailResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    unreadCount = json['unread_count'];
    lastRead = json['last_read'];
    activeStatusString = json['active_status_string'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['unread_count'] = this.unreadCount;
    data['active_status_string'] = this.activeStatusString;
    data['last_read'] = this.lastRead;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  ChatProfileDetail? toProfile;
  ChatProfileDetail? fromProfile;
  List<ChatDetail>? messages;

  Data({this.toProfile, this.fromProfile, this.messages});

  Data.fromJson(Map<String, dynamic> json) {
    toProfile = json['to_profile'] != null
        ? new ChatProfileDetail.fromJson(json['to_profile'])
        : null;
    fromProfile = json['from_profile'] != null
        ? new ChatProfileDetail.fromJson(json['from_profile'])
        : null;
    if (json['messages'] != null) {
      messages = <ChatDetail>[];
      json['messages'].forEach((v) {
        messages!.add(new ChatDetail.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();

    if (this.toProfile != null) {
      data['to_profile'] = this.toProfile!.toJson();
    }
    if (this.fromProfile != null) {
      data['from_profile'] = this.fromProfile!.toJson();
    }
    if (this.messages != null) {
      data['messages'] = this.messages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ChatProfileDetail {
  String? reference;
  // String? username;
  String? name;
  String? handle;
  String? icon;
  int? followerOrSupporterCount;

  ChatProfileDetail(
      {this.reference,
        // this.username,
        this.name,
        this.handle,
        this.icon,
        this.followerOrSupporterCount});

  ChatProfileDetail.fromJson(Map<String, dynamic> json) {
    reference = json['reference'];
    name = json['name'];
    // username = json['username'];
    handle = json['handle'];
    icon = json['icon'];
    followerOrSupporterCount = json['follower_or_supporter_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['reference'] = this.reference;
    data['name'] = this.name;
    data['handle'] = this.handle;
    data['icon'] = this.icon;
    data['follower_or_supporter_count'] = this.followerOrSupporterCount;
    return data;
  }
}

class ChatAttachment {
  String? type;
  String? originalName;
  int? size;
  String? url;
  String? mimetype;
  String? filename;

  ChatAttachment({this.type, this.originalName, this.size, this.url, this.filename, this.mimetype});

  ChatAttachment.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    originalName = json['originalName'];
    size = json['size'];
    // Strip localhost URL if present
    String rawUrl = json['url'] ?? '';
    if (rawUrl.startsWith('http://localhost')) {
      url = rawUrl.replaceFirst(RegExp(r'http://localhost:\d+'), '');
    } else {
      url = rawUrl;
    }
    filename = json['filename'];
    mimetype = json['mimetype'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['originalName'] = this.originalName;
    data['size'] = this.size;
    data['url'] = this.url;
    data['filename'] = this.filename;
    data['mimetype'] = this.mimetype;
    return data;
  }
}


class ChatDetail {
  String? messageId;
  String? text;
  String? timestamp;
  String? senderReference;
  bool isDeliveredToServer;
  List<ChatAttachment>? attachments;
  

  ChatDetail({
    this.messageId,
    this.text,
    this.timestamp,
    this.senderReference,
    this.isDeliveredToServer = true,
    this.attachments,
  });

  ChatDetail.fromJson(Map<String, dynamic> json)
      : messageId = json['message_id'],
        text = json['text'],
        senderReference = json['sender_reference'],
        isDeliveredToServer = json['is_delivered_to_server'] ?? true {
    // Parse and convert timestamp to IST during initialization
    if (json['timestamp'] != null) {
      try {
        DateTime utcTime = DateTime.parse(json['timestamp']).toUtc();
        DateTime istTime = utcTime.add(const Duration(hours: 5, minutes: 30));
        timestamp = DateFormat('yyyy-MM-dd HH:mm:ss').format(istTime);
      } catch (e) {
        //print('Error parsing timestamp: $e');
        timestamp = null;
      }
    }

    // Parse attachments
    if (json['attachments'] != null) {
      attachments = <ChatAttachment>[];
      json['attachments'].forEach((v) {
        attachments!.add(ChatAttachment.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message_id'] = messageId;
    data['text'] = text;
    data['timestamp'] = timestamp;
    data['sender_reference'] = senderReference;
    data['is_delivered_to_server'] = isDeliveredToServer;
    if (attachments != null) {
      data['attachments'] = attachments!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
