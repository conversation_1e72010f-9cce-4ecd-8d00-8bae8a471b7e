import 'package:flutter/material.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';

/// Helper class for FAQ deep linking functionality
class FaqLinkHelper {
  /// Generate a shareable link for a specific FAQ category
  static String generateCategoryShareLink(String categoryId) {
    return 'https://swadesic.com/faq?category=$categoryId';
  }

  /// Generate a shareable link for a specific FAQ question
  static String generateQuestionShareLink(
      String categoryId, String questionId) {
    return 'https://swadesic.com/faq?category=$categoryId&question=$questionId';
  }

  /// Test method to demonstrate FAQ deep linking
  static void testFaqDeepLinking(BuildContext context) {
    // Test category link
    debugPrint('Testing FAQ category link...');
    FaqNavigation.navigateToFaqCategory(context, 'pricing');

    // Test question link (uncomment to test)
    // Future.delayed(Duration(seconds: 2), () {
    //   debugPrint('Testing FAQ question link...');
    //   FaqNavigation.navigateToFaqQuestion(context, 'general', 'general_trial');
    // });
  }

  /// Show a dialog with shareable FAQ links for testing
  static void showFaqLinksDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('FAQ Deep Links'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Category Links:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                _buildLinkItem('General', generateCategoryShareLink('general')),
                _buildLinkItem('Pricing', generateCategoryShareLink('pricing')),
                _buildLinkItem(
                    'Dashboard', generateCategoryShareLink('dashboard')),
                _buildLinkItem('API', generateCategoryShareLink('api')),
                const SizedBox(height: 16),
                const Text('Question Links:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                _buildLinkItem('Free Trial',
                    generateQuestionShareLink('general', 'general_trial')),
                _buildLinkItem('Pricing Cost',
                    generateQuestionShareLink('pricing', 'pricing_cost')),
                _buildLinkItem('API Documentation',
                    generateQuestionShareLink('api', 'api_documentation')),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  static Widget _buildLinkItem(String title, String link) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(
            link,
            style: const TextStyle(fontSize: 12, color: Colors.blue),
          ),
          const SizedBox(height: 4),
        ],
      ),
    );
  }
}
