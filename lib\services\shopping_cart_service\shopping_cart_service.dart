

//happy
import 'package:swadesic/model/buyer_payment_options_responses/create_order_and_initiate_payment_response.dart';
import 'package:swadesic/model/shopping_cart_responses/add_to_cart_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_items_responses.dart';
import 'package:swadesic/model/shopping_cart_responses/order_create_response.dart';
import 'package:swadesic/model/shopping_cart_responses/payment_create_response.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';

import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class ShoppingCartServices {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  ShoppingCartServices() {
    httpService = HttpService();
  }

  // endregion

  // region Add Only Product Info
  Future<AddToCartResponse>addToCart({required int storeId,required String productReference, String? variantReference}) async {
    // get body [for POST request]
    var body =   {
      "product_reference": productReference,
      "storeid": storeId,
      "userid":AppConstants.appData.userId,
      "product_quantity": 1,
      "cart_product_status": "IN_CART"
    };

    // Add variant reference if provided
    if (variantReference != null && variantReference.isNotEmpty) {
      body["variant_reference"] = variantReference;
    }



    //print(body);

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.addToCart);

    // return response;

     return AddToCartResponse.fromJson(response);
  }
// endregion



  // region Get Shopping Cart Items
  Future<GetCartItemResponses> getShoppingCartItems() async {
    Map<String, dynamic> response;
    var url = "${AppConstants.getCartItems}${AppConstants.appData.userId}/";
    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    // return response;
    return GetCartItemResponses.fromJson(response);
  }
// endregion


// region Get cart Details
   Future<GetCartDetailsResponse> getCartDetail(List<int> cartIdList) async {
    // get body [for POST request]
    var body = {
      "user_id":AppConstants.appData.userId,
      "cart_items_list" :
        cartIdList

    };

    //print(body);

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.getCartDetail);
    // return response;

     //print(response);
    return GetCartDetailsResponse.fromJson(response);
  }
// endregion


  // region Get cart Price
  Future<GetCartPriceResponse> getCartPrice(List<int> cartIdList) async {
    // get body [for POST request]
    var body = {
      "user_id":AppConstants.appData.userId,
      "cart_items_list" :
      cartIdList

    };

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.getCartPrice);
    // return response;

    //print(response);
    return GetCartPriceResponse.fromJson(response);
  }
// endregion

// region Update Product Quantity
 updateProductQuantity(int productQuantity, int cartItemId) async {
    var body ={
      "product_quantity":productQuantity
    };
    //print("Body is $body");

    var url = "${AppConstants.updateCartProductQuantity}$cartItemId/";

    //print(url);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.patchApi(body,url);
    // return response;

    //print(response);
    //return GetCartDetailsResponse.fromJson(response);
  }
// endregion


// region Delete Cart Item
  deleteCartItem(int cartItemId) async {
    // get body [for POST request]
    var url = "${AppConstants.deleteSingleCartItem}$cartItemId/";


    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.deleteApiCall(url);
    // return response;

    //print(response);
  }
// endregion

  // region Order Create and payment initiate
  Future<CreateOrderAndInitiatePaymentResponse>createOrderAndInitiatePayment(
      {required List<String> cartItemId, required int addressId, required String mobileNumber, required String deliveryNotes, required var sellerNotes}) async {
    // get body [for POST request]
    var body = {

      "user_id": AppConstants.appData.userId,
      "cart_items": cartItemId,
      "billing_address_id":addressId,
      "phone_number": mobileNumber,
      "delivery_note": deliveryNotes,
      "seller_note":sellerNotes
    };
    //print(body);


    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.createOrderAndInitiatePayment);
    // return response;
    //print(response);

    return CreateOrderAndInitiatePaymentResponse.fromJson(response);
  }
// endregion

  // region Order Create
  Future<void>orderCreate(
      {required List<String> cartItemId, required int addressId, required String mobileNumber, required String deliveryNotes, required var sellerNotes}) async {
    // get body [for POST request]
    var body = {

      "userid": AppConstants.appData.userId,
      "cart_items": cartItemId,
      "billing_address_id":addressId,
      "phone_number": mobileNumber,
      "delivery_note": deliveryNotes,
      "seller_note":sellerNotes
    };
    //print(body);


    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.orderCreate);
    // return response;
    //print(response);

    // return OrderCreateResponse.fromJson(response);
  }
// endregion



  // region Payment create
  Future<PaymentCreateResponse>paymentCreate({required String orderNumber,
    required String deliveryFee,
    required String cartTotal,required String totalAmount,
  }) async {
    // get body [for POST request]
    var body = {
      "order_number":orderNumber,
      "delivery_fee":deliveryFee.replaceAll("₹",""),
      "cart_total" :cartTotal.replaceAll("₹",""),
      "total_amount" :totalAmount.replaceAll("₹",""),
      "user" :AppConstants.appData.userReference
    };
    //print(body);

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.paymentCreate);
    // return response;

    return PaymentCreateResponse.fromJson(response);
  }
// endregion


//region Add to wish list api call
  addToWishListApiCall({required List<String> productReferenceList}) async {
    // get body [for POST request]
    var body = {
      "user_reference":AppConstants.appData.userReference,
      "product_list":productReferenceList
    };
    //print(body);

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.addToWishList);
    // return response;

    //print(response);
    //return PaymentCreateResponse.fromJson(response);
  }
//endregion

//region Remove multiple cart item
   removeMultipleCartItems({required List<int> cartItemIdList}) async {
    // get body [for POST request]
    var body = {
      "cart_item_list": cartItemIdList
    };
    //print(body);

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.deleteApiCallBody(body, AppConstants.deleteMultipleCartItems);
    // return response;

    // return response ['message'];
    // //print(response);
  }
//endregion


}
