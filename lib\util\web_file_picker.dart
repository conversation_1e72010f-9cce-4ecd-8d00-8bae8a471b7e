import 'dart:async';
import 'package:flutter/foundation.dart';

// Import the appropriate implementation based on platform
import 'web_file_picker_impl.dart' if (dart.library.html) 'web_file_picker_impl_web.dart';

/// A class to handle file picking in web platform
class WebFilePicker {
  /// Pick a single image file from the web browser
  ///
  /// Returns a map containing the file bytes, name, and type
  static Future<Map<String, dynamic>?> pickImage() async {
    if (!kIsWeb) {
      return null;
    }

    return WebFilePickerImpl.pickImage();
  }

  /// Pick multiple image files from the web browser
  ///
  /// Returns a list of maps, each containing file bytes, name, and type
  static Future<List<Map<String, dynamic>>?> pickMultipleImages() async {
    if (!kIsWeb) {
      return null;
    }

    return WebFilePickerImpl.pickMultipleImages();
  }

  /// Pick any file from the web browser
  ///
  /// Returns a map containing the file bytes, name, and type
  static Future<Map<String, dynamic>?> pickFile({List<String>? allowedExtensions}) async {
    if (!kIsWeb) {
      return null;
    }

    return WebFilePickerImpl.pickFile(allowedExtensions: allowedExtensions);
  }

  /// Pick multiple files from the web browser
  ///
  /// Returns a list of maps, each containing file bytes, name, and type
  static Future<List<Map<String, dynamic>>?> pickMultipleFiles({List<String>? allowedExtensions}) async {
    if (!kIsWeb) {
      return null;
    }

    return WebFilePickerImpl.pickMultipleFiles(allowedExtensions: allowedExtensions);
  }
}

