import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_common_widgets.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'report_bloc.dart';

//region Report Screen
class ReportScreen extends StatefulWidget {
  final String reference;
  final bool isStore;
  final bool isProduct;
  final bool isPostComment;
  final bool isUser;

  const ReportScreen({Key? key, required this.reference, this.isStore = false,  this.isProduct = false,  this.isPostComment = false,  this.isUser  = false}) : super(key: key);

  @override
  State<ReportScreen> createState() => _ReportScreenState();
}
//endregion

class _ReportScreenState extends State<ReportScreen> {
  //region Build
  late ReportBloc reportBloc;
  //endregion
  //region Init
  @override
  void initState() {
    //print(widget.reference);
    reportBloc = ReportBloc(context,widget.reference,widget.isStore,widget.isProduct,widget.isPostComment,widget.isUser);
    reportBloc.init();
    super.initState();
  }
  //endregion
  //region Dispose
  @override
  void dispose() {
    reportBloc.dispose();
    super.dispose();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: appBar(),
        body: SafeArea(child: body()),
      ),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title:AppStrings.report,
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion


  //region Body
Widget body(){
    return Padding(
      padding: const EdgeInsets.all(10),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            sorry(),
            reasonList(),
            reasonTextField(),
            verticalSizedBox(20),
            additionalDetails(),
            verticalSizedBox(10),
            submit(),
            AppCommonWidgets.bottomListSpace(context: context)
          ],
        ),
      ),
    );
}
//endregion
//region Sorry
Widget sorry(){
    return Container(
        padding: const EdgeInsets.all(10),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //Sorry
            Text(AppStrings.sorryThatYouAre,style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),),
            verticalSizedBox(5),
            //We will inquire
            Text(AppStrings.weWillInquire,style: AppTextStyle.subTitle(textColor: AppColors.appBlack),),
          ],
        ));
}
//endregion


//region Reason list
Widget reasonList(){
    return StreamBuilder<bool>(
      stream: reportBloc.reasonSelectCtrl.stream,
      builder: (context, snapshot) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: reportBloc.reportReasons.length +1,
            itemBuilder: (context,index){
            if(index<reportBloc.reportReasons.length){
              return InkWell(
                  onTap: (){
                    reportBloc.onSelectReason(reason: reportBloc.reportReasons[index]);
                  },
                  child: ReportCommonWidgets.reportRadio(reason: reportBloc.reportReasons[index], reportBloc: reportBloc, isSelected: reportBloc.selectedReason == reportBloc.reportReasons[index]));

            }
            return InkWell(
                onTap: (){
                  reportBloc.onTapOther();
                },
                child: ReportCommonWidgets.reportRadio(reason:AppStrings.other, reportBloc: reportBloc, isSelected: reportBloc.isReasonFieldVisible));
            });
      }
    );
}
//endregion

//region Reason text field
Widget reasonTextField(){
    return StreamBuilder<bool>(
      stream: reportBloc.reasonSelectCtrl.stream,
      builder: (context, snapshot) {
        return Visibility(
          visible:reportBloc.isReasonFieldVisible,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 10),
            child:
            AppTextFields.allTextField(
              context: context,
              maxEntry: 200,
              textEditingController:reportBloc.reasonTextCtrl ,
              hintText: AppStrings.pleaseWriteDetails,
              onChanged: (){
                        reportBloc.selectedReason = reportBloc.reasonTextCtrl.text;
                        //print(reportBloc.selectedReason);
              }
            ),
            // Row(
            //   children: [
            //     Expanded(
            //       child: ReportCommonWidgets.reportTextField(textCtrl: reportBloc.reasonTextCtrl,
            //       onChange: (){
            //         reportBloc.selectedReason = reportBloc.reasonTextCtrl.text;
            //         //print(reportBloc.selectedReason);
            //       },
            //         hintText: AppStrings.pleaseWriteDetails,
            //          maxLine: 3,
            //         minLine: 1
            //       ),
            //     ),
            //     horizontalSizedBox(50)
            //   ],
            // ),
          ),
        );
      }
    );
}
//endregion


//region Additional details
Widget additionalDetails(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.additionalDetails,
          option: AppTextFields.allTextField(
            context: context,
            minLines: 5,
            maxEntry: 500,
            maxLines: 500,
            textEditingController:reportBloc.detailTextCtrl,
            hintText:  AppStrings.feelFree,
          ),
        ),

      ],
    );
}
//endregion


//region Submit
Widget submit(){
    return Row(
      children: [
        Expanded(
          child: AppCommonWidgets.activeButton(buttonName: AppStrings.submit, onTap: (){
            reportBloc.addReport();

          }),
        ),
      ],
    );
}
//endregion




}
