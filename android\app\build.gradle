def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
//apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    defaultConfig {
        multiDexEnabled true
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.sociallyx.dev"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 23
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
//        release {
//            signingConfig signingConfigs.release
//            isMinifyEnabled = true
//            isShrinkResources = true
//            proguardFiles(
//                    getDefaultProguardFile("proguard-android-optimize.txt"),
//                    "proguard-rules.pro"
//            )
//
//        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }


    //region Flavours
    flavorDimensions += "version"
    productFlavors {
        //Local flavor
        create("local") {
            dimension = "version"
            versionName = "1.4.9-1"
            versionCode = 21
            applicationId = "com.sociallyx.dev"
            apply plugin: 'com.google.gms.google-services'
            dependencies {
                implementation(platform("com.google.firebase:firebase-bom:32.6.0"))
                implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
                coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'

                implementation 'androidx.multidex:multidex:2.0.1'
                implementation("com.android.installreferrer:installreferrer:2.2")

            }
        }
        //Dev flavor
        create("dev") {
            dimension = "version"
            versionName = "2.0.2"
            versionCode = 25
            applicationId = "com.sociallyx.dev"
            apply plugin: 'com.google.gms.google-services'
            dependencies {
                implementation 'androidx.window:window:1.0.0'
                implementation 'androidx.window:window-java:1.0.0'
                implementation(platform("com.google.firebase:firebase-bom:32.6.0"))
                implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
                coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
                implementation("com.android.installreferrer:installreferrer:2.2")
                implementation 'androidx.multidex:multidex:2.0.1'

            }
        }
        //Qa flavor
        create("qa") {
            dimension = "version"
            versionName = "2.0.2"
            versionCode = 35
            applicationId = "com.sociallyx.test001"
            apply plugin: 'com.google.gms.google-services'
            dependencies {
                implementation(platform("com.google.firebase:firebase-bom:32.6.0"))
                implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
                coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
                implementation("com.android.installreferrer:installreferrer:2.2")
            }
        }
        //Production flavor
        create("prod") {
            dimension = "version"
            versionName = "2.0.6"
            versionCode = 39
            applicationId = "com.sociallyx.swadesic"
            apply plugin: 'com.google.gms.google-services'
            dependencies {
                implementation(platform("com.google.firebase:firebase-bom:32.6.0"))
                implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
                coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
                implementation("com.android.installreferrer:installreferrer:2.2")
            }
        }
    }
    //endregion

}

flutter {
    source '../..'
}

dependencies {
    implementation(platform("com.google.firebase:firebase-bom:32.6.0"))
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
    implementation 'androidx.multidex:multidex:2.0.1'
    // install referrer
    implementation("com.android.installreferrer:installreferrer:2.2")
}
