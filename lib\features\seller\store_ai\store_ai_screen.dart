import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/services/store_ai_service/store_ai_service.dart';

class StoreAiScreen extends StatefulWidget {
  const StoreAiScreen({Key? key}) : super(key: key);

  @override
  State<StoreAiScreen> createState() => _StoreAiScreenState();
}

class _StoreAiScreenState extends State<StoreAiScreen> {
  bool _isLoading = true;
  bool _isRegistered = false;
  late StoreAiService _storeAiService;

  @override
  void initState() {
    super.initState();
    _storeAiService = StoreAiService();
    _checkFeatureStatus();
  }

  Future<void> _checkFeatureStatus() async {
    final status = await _storeAiService.getFeatureRequestStatus();
    setState(() {
      _isRegistered = status;
      _isLoading = false;
    });
  }

  Future<void> _registerForStoreAi() async {
    setState(() => _isLoading = true);
    final success = await _storeAiService.addFeatureRequest();
    setState(() {
      _isRegistered = success;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        isCartVisible: false,
        isMembershipVisible: false,
        isDefaultMenuVisible: false,
        backgroundColor: AppColors.appWhite,
        onTapLeading: () => Navigator.pop(context),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Shape Your Store AI Before Others",
                    style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 14),
                  Text(
                    "Store AI is the digital persona of your store. It's not a chat bot—it represents the store itself.",
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 14),
                ],
              ),
            ),
            Image.asset(
              AppImages.storeAiInfoImage,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 18),
                  Text(
                    "How Store AI Works",
                    style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 18),
                  _buildFeatureItem(
                    "Built by You",
                    "As you set up your store—adding products, posts, and preferences—your Store AI takes shape.",
                  ),
                  _buildFeatureItem(
                    "Customizable",
                    "Define its tone, style, and branding to match your voice or make it speak like you.",
                  ),
                  _buildFeatureItem(
                    "Always Available",
                    "Store AI engages buyers 24/7, answering questions, handling orders, tracking shipments, and replying to comments and reviews.",
                  ),
                  _buildFeatureItem(
                    "Takes Repetitive Work Off Your Plate",
                    "It manages routine inquiries so you can focus on growth.",
                  ),
                  _buildFeatureItem(
                    "You're in Control",
                    "Enable or disable specific features—or turn off Store AI completely if needed.",
                  ),
                  _buildFeatureItem(
                    "Privacy First",
                    "Store AI doesn't access or leak your financial details or private data.",
                  ),
                  const SizedBox(height: 24),
                  Text(
                    "How to Start Shaping Your Store AI Today",
                    style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 24),
                  Text(
                      "Your Store AI learns from what you build. The more you refine your store, the better it gets. Here's what you can do right now:",
                      style: AppTextStyle.settingText(textColor: AppColors.appBlack),
                    ),
                  const SizedBox(height: 24),
                  _buildFeatureItem(
                    "Add & Organize Products",
                    "Store AI understands your catalog better when you provide clear descriptions, categories, and details.",
                  ),
                  _buildFeatureItem(
                    "Post & Engage",
                    "Your posts, comments, and interactions help Store AI's understand well about your products and brand so it learns what to respond.",
                  ),
                  _buildFeatureItem(
                    "Set Store Preferences",
                    "Define your store's fulfillment, return & refund policies, and common responses so Store AI can assist buyers accurately.",
                  ),
                  const SizedBox(height: 24),
                  Text(
                    "Start shaping now, and when Store AI goes live, your Store AI will be ahead of the rest.",
                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 150),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading || _isRegistered ? null : _registerForStoreAi,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.appBlack,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14),
                        ),
                        disabledBackgroundColor: AppColors.textFieldFill2,
                      ),
                      child: Text(
                        _isLoading 
                          ? "Loading..." 
                          : _isRegistered 
                            ? "Requested"
                            : "Try it Before Others",
                        style: AppTextStyle.access0(
                          textColor: _isRegistered ? AppColors.disableBlack : AppColors.appWhite
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 75),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10, left: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "• ",
                    style: AppTextStyle.heading4SemiBold(
                      textColor: AppColors.appBlack,
                    ).copyWith(
                      fontSize: CommonMethods.fontSizeChanger(size: 20),
                    ),
                  ),
                  TextSpan(
                    text: title,
                    style: AppTextStyle.heading4SemiBold(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              description,
              style: AppTextStyle.settingText(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }
}
