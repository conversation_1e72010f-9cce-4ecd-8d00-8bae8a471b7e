import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/all_feed/all_feed_bloc.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';

enum AllFeedPaginationState { Loading, Done, Empty, Null }

class AllFeedPagination {
  //region Context
  late BuildContext context;
  late AllFeedBloc allFeedBloc;
  int limit = 5;
  AllFeedPaginationState currentApiCallStatus = AllFeedPaginationState.Done;

  // bool isLoadingPaginationData = false;
  AllFeedPaginationState currentPaginationState =
      AllFeedPaginationState.Loading;

  //endregion

  //region Controller
  final feedPaginationStateCtrl =
      StreamController<AllFeedPaginationState>.broadcast();

  //endregion

  //region Constructor
  AllFeedPagination(this.context, this.allFeedBloc) {
    //Add initial state in pagination state
    feedPaginationStateCtrl.sink.add(AllFeedPaginationState.Done);
    //Scroll listener
    allFeedBloc.scrollController.addListener(() {
      scrollListener();
    });
    //Pagination controller listener
    feedPaginationStateCtrl.stream.listen((AllFeedPaginationState state) {
      feedPaginationControllerListener(state: state);
    });
  }

  void dispose() {
    feedPaginationStateCtrl.close();
  }

  //endregion

  //region Feed page Controller listener
  void feedPaginationControllerListener(
      {required AllFeedPaginationState state}) {
    currentPaginationState = state;
    //print("Status of pagination${state.name}");
  }

  //endregion

  //region Scroll listener
  void scrollListener() async {
    // If Page state is empty then return - exactly like feed_pagination.dart
    if (currentPaginationState == AllFeedPaginationState.Empty) {
      return;
    }

    // Using the exact same logic as in feed_pagination.dart
    if (allFeedBloc.feedList.length >= 10 &&
        allFeedBloc.scrollController.offset >=
            allFeedBloc.scrollController.position.maxScrollExtent &&
        !allFeedBloc.scrollController.position.outOfRange) {
      // Fetch more feed posts when list is scrolled to the bottom
      await getPaginationFeeds();
    }
  }
  //endregion

  //region Get pagination feeds
  Future<void> getPaginationFeeds() async {
    try {
      //If api call status is Loading then return
      if (currentApiCallStatus == AllFeedPaginationState.Loading ||
          allFeedBloc.isPaginationLoading) {
        return;
      }

      // Set loading state
      allFeedBloc.isPaginationLoading = true;
      feedPaginationStateCtrl.sink.add(AllFeedPaginationState.Loading);
      currentApiCallStatus = AllFeedPaginationState.Loading;

      // Fetch data with a slight delay to prevent UI stuttering
      await Future.delayed(const Duration(milliseconds: 100));

      // Execute the API call without using context
      await _fetchAndProcessFeedItems();
    } catch (e) {
      //Done
      feedPaginationStateCtrl.sink.add(AllFeedPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = AllFeedPaginationState.Done;
      allFeedBloc.isPaginationLoading = false;
    }
  }

  // Helper method to fetch and process feed items without using context across async gaps
  Future<void> _fetchAndProcessFeedItems() async {
    // Get data models synchronously before any async operations
    final postDataModel = Provider.of<PostDataModel>(context, listen: false);
    final productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    final currentContext = context;

    // Now we can safely use async operations
    try {
      //Api call - use the new lean all feed API
      final feedList = await PostService().getLeanAllFeeds(
          limit: limit,
          offset: allFeedBloc.feedList.length,
          context: currentContext);

      //If feed list is empty then return
      if (feedList.isEmpty) {
        //Empty
        feedPaginationStateCtrl.sink.add(AllFeedPaginationState.Empty);
        //Current api call status is Empty
        currentApiCallStatus = AllFeedPaginationState.Empty;
        allFeedBloc.isPaginationLoading = false;
        allFeedBloc.hasMoreData = false;
        return;
      }

      // Process the new items in a microtask to avoid blocking the UI
      Future.microtask(() {
        //Add feed to the feed list variable
        allFeedBloc.feedList.addAll(feedList);

        //Filter post and product
        for (var postAndProduct in feedList) {
          if (postAndProduct is PostDetail) {
            //Add data in post data model feed
            postDataModel.addPostIntoList(postList: [postAndProduct]);
          } else if (postAndProduct is Product) {
            //Add data to product data model
            productDataModel.addProductIntoList(products: [postAndProduct]);
          }
        }

        // Update the UI in the bloc
        allFeedBloc.currentApiCallStatus = AllFeedState.Success;
        allFeedBloc.feedStateCtrl.sink.add(AllFeedState.Success);

        //Done
        feedPaginationStateCtrl.sink.add(AllFeedPaginationState.Done);
        //Current api call status is Done
        currentApiCallStatus = AllFeedPaginationState.Done;
        allFeedBloc.isPaginationLoading = false;
      });
    } catch (e) {
      // Handle errors
      feedPaginationStateCtrl.sink.add(AllFeedPaginationState.Done);
      currentApiCallStatus = AllFeedPaginationState.Done;
      allFeedBloc.isPaginationLoading = false;
    }
  }
  //endregion
}
