import 'dart:async';
import 'dart:html' as html;
import 'dart:typed_data';

/// Implementation for web platform
class WebFilePickerImpl {
  /// Pick a single image file from the web browser
  /// 
  /// Returns a map containing the file bytes, name, and type
  static Future<Map<String, dynamic>?> pickImage() async {
    final completer = Completer<Map<String, dynamic>?>();
    
    // Create file input element
    final input = html.FileUploadInputElement();
    input.accept = 'image/*';
    input.click();
    
    // Listen for file selection
    input.onChange.listen((event) {
      final files = input.files;
      if (files != null && files.isNotEmpty) {
        final file = files[0];
        final reader = html.FileReader();
        
        reader.onLoad.listen((event) {
          final result = reader.result as Uint8List;
          completer.complete({
            'bytes': result,
            'name': file.name,
            'type': file.type,
            'size': file.size,
          });
        });
        
        reader.onError.listen((event) {
          completer.complete(null);
        });
        
        reader.readAsArrayBuffer(file);
      } else {
        completer.complete(null);
      }
    });
    
    // Handle when user cancels the picker
    input.onAbort.listen((event) {
      completer.complete(null);
    });
    
    return completer.future;
  }
  
  /// Pick multiple image files from the web browser
  /// 
  /// Returns a list of maps, each containing file bytes, name, and type
  static Future<List<Map<String, dynamic>>?> pickMultipleImages() async {
    final completer = Completer<List<Map<String, dynamic>>?>();
    
    // Create file input element
    final input = html.FileUploadInputElement();
    input.accept = 'image/*';
    input.multiple = true;
    input.click();
    
    // Listen for file selection
    input.onChange.listen((event) {
      final files = input.files;
      if (files != null && files.isNotEmpty) {
        final results = <Map<String, dynamic>>[];
        var loadedCount = 0;
        
        for (var i = 0; i < files.length; i++) {
          final file = files[i];
          final reader = html.FileReader();
          
          reader.onLoad.listen((event) {
            final result = reader.result as Uint8List;
            results.add({
              'bytes': result,
              'name': file.name,
              'type': file.type,
              'size': file.size,
            });
            
            loadedCount++;
            if (loadedCount == files.length) {
              completer.complete(results);
            }
          });
          
          reader.onError.listen((event) {
            loadedCount++;
            if (loadedCount == files.length) {
              completer.complete(results);
            }
          });
          
          reader.readAsArrayBuffer(file);
        }
      } else {
        completer.complete(null);
      }
    });
    
    // Handle when user cancels the picker
    input.onAbort.listen((event) {
      completer.complete(null);
    });
    
    return completer.future;
  }
  
  /// Pick any file from the web browser
  /// 
  /// Returns a map containing the file bytes, name, and type
  static Future<Map<String, dynamic>?> pickFile({List<String>? allowedExtensions}) async {
    final completer = Completer<Map<String, dynamic>?>();
    
    // Create file input element
    final input = html.FileUploadInputElement();
    
    // Set accepted file types if provided
    if (allowedExtensions != null && allowedExtensions.isNotEmpty) {
      input.accept = allowedExtensions.map((ext) => '.$ext').join(',');
    }
    
    input.click();
    
    // Listen for file selection
    input.onChange.listen((event) {
      final files = input.files;
      if (files != null && files.isNotEmpty) {
        final file = files[0];
        final reader = html.FileReader();
        
        reader.onLoad.listen((event) {
          final result = reader.result as Uint8List;
          completer.complete({
            'bytes': result,
            'name': file.name,
            'type': file.type,
            'size': file.size,
          });
        });
        
        reader.onError.listen((event) {
          completer.complete(null);
        });
        
        reader.readAsArrayBuffer(file);
      } else {
        completer.complete(null);
      }
    });
    
    // Handle when user cancels the picker
    input.onAbort.listen((event) {
      completer.complete(null);
    });
    
    return completer.future;
  }
  
  /// Pick multiple files from the web browser
  /// 
  /// Returns a list of maps, each containing file bytes, name, and type
  static Future<List<Map<String, dynamic>>?> pickMultipleFiles({List<String>? allowedExtensions}) async {
    final completer = Completer<List<Map<String, dynamic>>?>();
    
    // Create file input element
    final input = html.FileUploadInputElement();
    
    // Set accepted file types if provided
    if (allowedExtensions != null && allowedExtensions.isNotEmpty) {
      input.accept = allowedExtensions.map((ext) => '.$ext').join(',');
    }
    
    input.multiple = true;
    input.click();
    
    // Listen for file selection
    input.onChange.listen((event) {
      final files = input.files;
      if (files != null && files.isNotEmpty) {
        final results = <Map<String, dynamic>>[];
        var loadedCount = 0;
        
        for (var i = 0; i < files.length; i++) {
          final file = files[i];
          final reader = html.FileReader();
          
          reader.onLoad.listen((event) {
            final result = reader.result as Uint8List;
            results.add({
              'bytes': result,
              'name': file.name,
              'type': file.type,
              'size': file.size,
            });
            
            loadedCount++;
            if (loadedCount == files.length) {
              completer.complete(results);
            }
          });
          
          reader.onError.listen((event) {
            loadedCount++;
            if (loadedCount == files.length) {
              completer.complete(results);
            }
          });
          
          reader.readAsArrayBuffer(file);
        }
      } else {
        completer.complete(null);
      }
    });
    
    // Handle when user cancels the picker
    input.onAbort.listen((event) {
      completer.complete(null);
    });
    
    return completer.future;
  }
}
