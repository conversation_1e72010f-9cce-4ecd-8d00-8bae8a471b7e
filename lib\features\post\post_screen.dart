import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/adding_post_progress/adding_post_progress.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/post_pagination.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/model/app_data/app_data.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Post screen
enum PostScreenContentType {
  userPosts,    // Posts created BY the user/store
  storeReviews, // Reviews/comments ABOUT the store
}

class PostScreen extends StatefulWidget {
  final bool isRepost;
  final String storeOrUserReference;
  final ScrollController previousScrollController;
  final PostScreenContentType contentType;

  const PostScreen({
    super.key,
    required this.storeOrUserReference,
    required this.previousScrollController,
    this.isRepost = false,
    this.contentType = PostScreenContentType.userPosts,
  });

  @override
  State<PostScreen> createState() => _PostScreenState();
}
//endregion

class _PostScreenState extends State<PostScreen>
    with AutomaticKeepAliveClientMixin<PostScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //Post screen
  late PostScreenBloc postScreenBloc;

  //region Init
  @override
  void initState() {
    postScreenBloc = PostScreenBloc(
      context,
      widget.storeOrUserReference,
      widget.previousScrollController,
      widget.isRepost,
      widget.contentType,
    );
    postScreenBloc.init();
    super.initState();
  }

  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return body();
  }

  //endregion

  //region Body
  Widget body() {
    return Stack(
      children: [
        widget.isRepost ? repostList() : postList(),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Consumer<PostDataModel>(
            builder: (BuildContext context, PostDataModel data, Widget? child) {
              String currentReference = AppConstants.appData.isUserView!
                  ? AppConstants.appData.userReference!
                  : AppConstants.appData.storeReference!;
              return Visibility(
                visible: data.postingStatus &&
                    widget.storeOrUserReference == currentReference,
                child: Container(
                  // height: CommonMethods.textHeight(context: context, textStyle:AppTextStyle.contentHeading0(textColor: AppColors.appWhite), ) *2,
                  // margin: const EdgeInsets.only(top: kToolbarHeight),
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: BoxDecoration(
                      color: AppColors.appBlack,
                      boxShadow: AppColors.toastMessageShadow),
                  width: double.infinity,
                  child: Text(
                    AppStrings.posting,
                    textAlign: TextAlign.start,
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appWhite),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

//endregion

  //region Post list
  Widget postList() {
    return StreamBuilder<PostScreenState>(
      stream: postScreenBloc.postStateCtrl.stream,
      initialData: PostScreenState.Loading,
      builder: (context, snapshot) {
        if (snapshot.data == PostScreenState.Success) {
          return Consumer<PostDataModel>(
            builder: (context, data, child) {
              List<PostDetail> postList = [];

              if (widget.contentType == PostScreenContentType.storeReviews) {
                // For store reviews, show all comments/reviews in the data model
                // (these are already filtered by the StoreCommentService)
                postList = data.allPostDetailList
                    .where((element) =>
                        element.postOrCommentReference!.startsWith("CO") &&
                        element.commentType == "REVIEW")
                    .toList();
              } else {
                // Original logic: posts and comments created BY the user/store
                postList = data.allPostDetailList
                    .where((element) =>
                        element.createdBy!.userOrStoreReference ==
                            widget.storeOrUserReference &&
                        (element.postOrCommentReference!.startsWith("P") ||
                         element.postOrCommentReference!.startsWith("CO")))
                    .toList();
              }
              //Short by time
              postList.sort((b, a) => DateTime.parse(a.createdDate!)
                  .compareTo(DateTime.parse(b.createdDate!)));
              //If empty
              if (postList.isEmpty) {
                return RefreshIndicator(
                  color: AppColors.brandBlack,
                  onRefresh: () async {
                    postScreenBloc.getAllUserOrStorePosts();
                  },
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Container(
                        alignment: Alignment.center,
                        height:
                            CommonMethods.calculateWebWidth(context: context),
                        child: const NoResult(message: AppStrings.noPostFound)
                        // child: Text(
                        //   AppStrings.noPostFound,
                        //   style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                        // ),
                        ),
                  ),
                );
              }
              return RefreshIndicator(
                color: AppColors.brandBlack,
                onRefresh: () async {
                  await postScreenBloc.init();
                },
                child: ListView.separated(
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 20),
                  controller: postScreenBloc.postScreenScrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: postList.length + 1,
                  itemBuilder: (context, index) {
                    //
                    if (index < postList.length) {
                      return PostCard(
                        postDetail: postList[index],
                        onTapDelete: () {
                          postScreenBloc.confirmDelete(
                              postDetail: postList[index]);
                        },
                        onTapDrawer: () {
                          postScreenBloc.onTapDrawer(
                              postDetail: postList[index]);
                        },
                        onTapEdit: () {
                          postScreenBloc.goToEditPost(
                              postDetail: postList[index]);
                        },
                        onTapHeart: () {
                          postScreenBloc.onTapHeart(
                              postDetail: postList[index]);
                        },
                        onTapReport: () {},
                        onTapShare: () {
                          postScreenBloc.onTapShare(
                              postDetail: postList[index]);
                        },
                        onTapProfileImage: () {
                          postScreenBloc.onTapUserOrStoreIcon(
                              reference: postList[index]
                                  .createdBy!
                                  .userOrStoreReference!);
                        },
                        onTapPost: () {
                          postScreenBloc.goToSinglePostView(
                              postReference:
                                  postList[index].postOrCommentReference!);
                        },
                      );
                    }
                    //Pagination loading
                    else {
                      return paginationLoading();
                    }
                  },
                ),
              );
            },
          );
        } else if (snapshot.data == PostScreenState.Failed) {
          return AppCommonWidgets.errorWidget(
            errorMessage: AppStrings.commonErrorMessage,
            onTap: () {
              postScreenBloc.getAllUserOrStorePosts();
            },
          );
        } else if (snapshot.data == PostScreenState.Empty) {
          return RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await postScreenBloc.init();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                alignment: Alignment.center,
                height: CommonMethods.calculateWebWidth(context: context),
                child: const NoResult(message: AppStrings.noPostFound),
              ),
            ),
          );
        } else if (snapshot.data == PostScreenState.Loading) {
          return Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height,
            child: AppCommonWidgets.appCircularProgress(),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }
//endregion

// //region Post list
//   Widget postList() {
//     return StreamBuilder<PostScreenState>(
//       stream: postScreenBloc.postStateCtrl.stream,
//       initialData: PostScreenState.Loading,
//       builder: (context, snapshot) {
//         //print("Post detail staue in repost is ${snapshot.data}");
//
//         if (snapshot.data == PostScreenState.Success) {
//
//           return Consumer<PostDataModel>(
//             builder: (context, data, child) {
//               // Get reference to the PostDataModel using Provider
//               var postDataModel = Provider.of<PostDataModel>(context, listen: true);
//               // Get reference to the ProductDataModel using Provider
//               var productDataModel = Provider.of<ProductDataModel>(context, listen: true);
//               List<dynamic> postList = [];
//               //Take out only post and created by logged in user
//               //If repost
//               if (postScreenBloc.isRepost) {
//                 for (var postAndProduct in postScreenBloc.postList) {
//
//                   //If incoming object is Post detail
//                   if (postAndProduct is PostDetail) {
//                     //Take out posts who's post reference is same as the postAndProduct
//                     postList.addAll(postDataModel.allPostDetailList.where((element) => element.postOrCommentReference == postAndProduct.postOrCommentReference)) ;
//
//                     // //print('Post: ${postAndProduct.postOrCommentReference}');
//                   }
//                   //If incoming object is Product detail
//                   else if (postAndProduct is Product) {
//                     //Take out product who's post reference is same as the postAndProduct
//                     postList.addAll(productDataModel.allProducts.where((element) => element.productReference == postAndProduct.productReference)) ;
//
//                     // //print('Product: ${postAndProduct.productReference}');
//                   }
//
//
//                 }
//
//                 // postList = data.allPostDetailList
//                 //     .where((element) =>
//                 //         element.createdBy!.userOrStoreReference == widget.storeOrUserReference &&
//                 //         element.postOrCommentReference!.startsWith("P") &&
//                 //         element.contentType == EntityType.REPOST.name &&
//                 //         postScreenBloc.postList.any((e) => e.postOrCommentReference == element.postOrCommentReference))
//                 //     .toList();
//               }
//               //Else normal post
//               else {
//                 for (var postAndProduct in postScreenBloc.postList) {
//
//                   //If incoming object is Post detail
//                   if (postAndProduct is PostDetail) {
//                     //Take out posts who's post reference is same as the postAndProduct
//                     postList.addAll(postDataModel.allPostDetailList.where((element) => element.postOrCommentReference == postAndProduct.postOrCommentReference)) ;
//
//                     // //print('Post: ${postAndProduct.postOrCommentReference}');
//                   }
//                   //If incoming object is Product detail
//                   else if (postAndProduct is Product) {
//                     //Take out product who's post reference is same as the postAndProduct
//                     postList.addAll(productDataModel.allProducts.where((element) => element.productReference == postAndProduct.productReference)) ;
//
//                     // //print('Product: ${postAndProduct.productReference}');
//                   }
//
//
//                 }
//
//                 // postList = data.allPostDetailList
//                 //     .where((element) =>
//                 //         element.createdBy!.userOrStoreReference == widget.storeOrUserReference &&
//                 //         element.postOrCommentReference!.startsWith("P") &&
//                 //         postScreenBloc.postList.any((e) => e.postOrCommentReference == element.postOrCommentReference))
//                 //     .toList();
//               }
//               //Short by time
//               postList.sort((b, a) => DateTime.parse(a.createdDate!).compareTo(DateTime.parse(b.createdDate!)));
//               //If empty
//               if (postList.isEmpty) {
//                 return RefreshIndicator(
//                   color: AppColors.brandGreen,
//                   onRefresh: () async {
//                     postScreenBloc.getAllUserOrStorePosts();
//                   },
//                   child: SingleChildScrollView(
//                     physics: const AlwaysScrollableScrollPhysics(),
//                     child: Container(
//                         alignment: Alignment.center,
//                         height: CommonMethods.calculateWebWidth(context: context),
//                         child: const NoResult(message: AppStrings.noPostFound)),
//                   ),
//                 );
//               }
//               return RefreshIndicator(
//                 color: AppColors.brandGreen,
//                 onRefresh: () async {
//                   await postScreenBloc.init();
//                 },
//                 child: ListView.builder(
//                   controller: postScreenBloc.postScreenScrollController,
//                   physics: const AlwaysScrollableScrollPhysics(),
//                   shrinkWrap: true,
//                   itemCount: postList.length + 1,
//                   itemBuilder: (context, index) {
//                     if (index < postList.length) {
//
//                       //If Post
//                       if(postList[index] is PostDetail){
//                         return PostCard(
//                           postDetail: postList[index],
//                           onTapDelete: () {
//                             postScreenBloc.confirmDelete(postDetail: postList[index]);
//                           },
//                           onTapDrawer: () {
//                             postScreenBloc.onTapDrawer(postDetail: postList[index]);
//                           },
//                           onTapEdit: () {
//                             postScreenBloc.goToEditPost(postDetail: postList[index]);
//                           },
//                           onTapHeart: () {
//                             postScreenBloc.onTapHeart(postDetail: postList[index]);
//                           },
//
//                           onTapShare: () {
//                             postScreenBloc.onTapShare(postDetail: postList[index]);
//                           },
//                           onTapProfileImage: () {
//                             postScreenBloc.onTapUserOrStoreIcon(reference: postList[index].createdBy!.userOrStoreReference!);
//                           },
//                           onTapPost: () {
//                             postScreenBloc.goToSinglePostView(postReference: postList[index].postOrCommentReference!);
//                           },
//                         );
//                       }
//                       //If product
//                       if(postList[index]  is Product){
//                         return ProductDetailFullCard(product:postList[index] ,isFromAddProduct: false,isFullView: false);
//                       }
//                       return const SizedBox();
//
//                     }
//                     //Pagination loading
//                     else {
//                       return paginationLoading();
//                     }
//                   },
//                 ),
//               );
//             },
//           );
//         }
//         else if (snapshot.data == PostScreenState.Failed) {
//           return AppCommonWidgets.errorWidget(
//             errorMessage: AppStrings.commonErrorMessage,
//             onTap: () {
//               postScreenBloc.getAllUserOrStorePosts();
//             },
//           );
//         } else if (snapshot.data == PostScreenState.Empty) {
//           return RefreshIndicator(
//             color: AppColors.brandGreen,
//             onRefresh: () async {
//               await postScreenBloc.init();
//             },
//             child: SingleChildScrollView(
//               physics: const AlwaysScrollableScrollPhysics(),
//               child: Container(
//                 alignment: Alignment.center,
//                 height: CommonMethods.calculateWebWidth(context: context),
//                 child: const NoResult(message: AppStrings.noPostFound),
//               ),
//             ),
//           );
//         } else if (snapshot.data == PostScreenState.Loading) {
//           return Container(
//             alignment: Alignment.center,
//             height: MediaQuery.of(context).size.height,
//             child: AppCommonWidgets.appCircularProgress(),
//           );
//         } else {
//           return const SizedBox();
//         }
//       },
//     );
//   }
//
// //endregion

//region Repost  list
  Widget repostList() {
    return StreamBuilder<PostScreenState>(
      stream: postScreenBloc.postStateCtrl.stream,
      initialData: PostScreenState.Loading,
      builder: (context, snapshot) {
        //print("Post detail staue in repost is ${snapshot.data}");

        if (snapshot.data == PostScreenState.Success) {
          return RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await postScreenBloc.init();
            },
            child: ListView.separated(
              separatorBuilder: (context, index) => const SizedBox(height: 20),
              controller: postScreenBloc.postScreenScrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: postScreenBloc.postList.length + 1,
              itemBuilder: (context, index) {
                if (index < postScreenBloc.postList.length) {
                  //If post
                  if (postScreenBloc.postList[index] is PostDetail) {
                    return Consumer<PostDataModel>(
                      builder: (BuildContext context, PostDataModel value,
                          Widget? child) {
                        PostDetail postDetail = value.allPostDetailList
                            .firstWhere((element) =>
                                element.postOrCommentReference ==
                                postScreenBloc
                                    .postList[index].postOrCommentReference);

                        return PostCard(
                          postDetail: postDetail,
                          onTapDelete: () {
                            postScreenBloc.confirmDelete(
                                postDetail: postDetail);
                          },
                          onTapDrawer: () {
                            postScreenBloc.onTapDrawer(postDetail: postDetail);
                          },
                          onTapEdit: () {
                            postScreenBloc.goToEditPost(postDetail: postDetail);
                          },
                          onTapHeart: () {
                            postScreenBloc.onTapHeart(postDetail: postDetail);
                          },
                          onTapShare: () {
                            postScreenBloc.onTapShare(postDetail: postDetail);
                          },
                          onTapProfileImage: () {
                            postScreenBloc.onTapUserOrStoreIcon(
                                reference: postDetail
                                    .createdBy!.userOrStoreReference!);
                          },
                          onTapPost: () {
                            postScreenBloc.goToSinglePostView(
                                postReference:
                                    postDetail.postOrCommentReference!);
                          },
                        );
                      },
                    );
                  }

                  //If product
                  else if (postScreenBloc.postList[index] is Product) {
                    // return const SizedBox();
                    // return Container(
                    //   margin: const EdgeInsets.all(20),
                    //   height: 100,width: 100,color: Colors.orange,);
                    return ProductDetailFullCard(
                        product: postScreenBloc.postList[index],
                        isFromAddProduct: false,
                        isFullView: false);
                  }
                  return const SizedBox();
                }
                //Pagination loading
                else {
                  return paginationLoading();
                }
              },
            ),
          );
        } else if (snapshot.data == PostScreenState.Failed) {
          return AppCommonWidgets.errorWidget(
            errorMessage: AppStrings.commonErrorMessage,
            onTap: () {
              postScreenBloc.getAllUserOrStorePosts();
            },
          );
        } else if (snapshot.data == PostScreenState.Empty) {
          return RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await postScreenBloc.init();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                alignment: Alignment.center,
                height: CommonMethods.calculateWebWidth(context: context),
                child: const NoResult(message: AppStrings.noRepostFound),
              ),
            ),
          );
        } else if (snapshot.data == PostScreenState.Loading) {
          return Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height,
            child: AppCommonWidgets.appCircularProgress(),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }

//endregion

//region Pagination loading
  Widget paginationLoading() {
    return StreamBuilder<PostPaginationState>(
        stream: postScreenBloc.postPagination.postPaginationStateCtrl.stream,
        initialData: PostPaginationState.Loading,
        builder: (context, snapshot) {
          //Empty if current state and the post list is smaller then 10
          if (postScreenBloc.postPagination.currentPaginationState ==
                  PostPaginationState.Empty ||
              postScreenBloc.postList.length < 10) {
            return const SizedBox();
          }
          //Loading and
          if (snapshot.data == PostPaginationState.Loading) {
            return AppCommonWidgets.appCircularProgress(
                isPaginationProgress: true);
          }
          return const SizedBox();
        });
  }
//endregion
}
