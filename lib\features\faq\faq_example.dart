import 'package:flutter/material.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/features/widgets/app_buttons/app_buttons.dart';

class FaqExample extends StatelessWidget {
  const FaqExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'FAQ Example',
          style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
        ),
        backgroundColor: AppColors.appWhite,
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'This is an example of how to navigate to the FAQ screen',
                style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              AppButtons().button1(
                context: context,
                buttonName: 'Open FAQ Screen',
                onTap: () {
                  FaqNavigation.navigateToFaqScreen(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
