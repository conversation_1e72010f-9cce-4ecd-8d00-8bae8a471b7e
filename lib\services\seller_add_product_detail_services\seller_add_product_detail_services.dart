

//happy
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields_bloc.dart';
import 'package:swadesic/model/seller_add_product_detail_api_response/seller_add_product_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class SellerAddProductDetailServices {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  SellerAddProductDetailServices() {
    httpService = HttpService();
  }

  // endregion

  // region Add Only Product Info
  Future<AddProductDetailResponse> sellerAddProduct(
      {
        required String storeReference,
        required Product product
      }) async {
    // get body [for POST request]
    var body = {
      "product_name": AddEditProductFieldsBloc.productNameTextCtrl.text,
      "brand_name": AddEditProductFieldsBloc.brandNameTextCtrl.text,
      "product_slug": AddEditProductFieldsBloc.productSlugTextCtrl.text,
      "product_code": AddEditProductFieldsBloc.productCodeTextCtrl.text,
      "product_category": AddEditProductFieldsBloc.productCategoryTextCtrl.text,
      "product_description": AddEditProductFieldsBloc.productDescNameTextCtrl.text,
      "created_by": AppConstants.appData.userId,
      "modified_by": AppConstants.appData.userId,
      "promotion_link":CommonMethods.addHttpAndHttps(url:AddEditProductFieldsBloc.promoLinkTextCtrl.text),
      "hashtags": product.hashTag,
      "in_stock": int.parse(AddEditProductFieldsBloc.inStockTextCtrl.text),
      "mrp_price": int.parse(AddEditProductFieldsBloc.mrpTextCtrl.text),
      "selling_price": int.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text),
      "store_reference": storeReference,
      "swadeshi_owned": product.swadeshiOwned,
      "swadeshi_made": product.swadeshiMade,
      "swadeshi_brand": product.swadeshiBrand,
      "targeted_gender":product.targetGender,
      if (product.options != null && product.options!.isNotEmpty)
        "options": product.options

    };
      //print(body);

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.sellerAddProduct);
    // return response;

    return AddProductDetailResponse.fromJson(response);
  }
// endregion

}
