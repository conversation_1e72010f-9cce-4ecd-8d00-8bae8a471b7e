import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';

enum LevelBadgeType { store, user }

class LevelBadge extends StatelessWidget {
  final String level;
  final LevelBadgeType badgeType;
  final double width;
  final double height;
  final double? borderWidth;
  final double? fontSize;

  const LevelBadge({
    super.key,
    required this.level,
    required this.badgeType,
    this.width = 32,
    this.height = 32,
    this.borderWidth,
    this.fontSize,
  });

  /// Static method to create a level badge with custom dimensions
  /// This method can be used throughout the app for consistent badge creation
  static Widget createLevelBadge({
    required String level,
    required LevelBadgeType badgeType,
    required double width,
    required double height,
    double? borderWidth,
    double? fontSize,
  }) {
    return LevelBadge(
      level: level,
      badgeType: badgeType,
      width: width,
      height: height,
      borderWidth: borderWidth,
      fontSize: fontSize,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Determine badge styling based on type
    Color backgroundColor;
    Color textColor;
    Color? borderColor;
    String levelText;

    switch (badgeType) {
      case LevelBadgeType.store:
        backgroundColor = AppColors.appRichBlack;
        textColor = AppColors.appWhite;
        borderColor = null;
        levelText = 'S$level';
        break;
      case LevelBadgeType.user:
        backgroundColor = AppColors.appWhite;
        textColor = AppColors.appRichBlack;
        borderColor = AppColors.appRichBlack;
        levelText = level.toString();
        break;
    }

    // Calculate border radius based on dimensions
    double borderRadius = height * 0.4; // 40% of height for rounded appearance

    // Calculate font size based on badge dimensions or use custom fontSize
    double calculatedFontSize =
        fontSize ?? (height * 0.4); // 40% of height or custom

    // Use custom border width or default based on badge type
    double calculatedBorderWidth =
        borderWidth ?? (badgeType == LevelBadgeType.user ? 1.5 : 2.0);

    // For store badges, add white border padding
    if (badgeType == LevelBadgeType.store) {
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: AppColors.appWhite,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: EdgeInsets.all(calculatedBorderWidth), // White border padding
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(borderRadius - 2),
          ),
          child: Center(
            child: Text(
              levelText,
              style: TextStyle(
                color: textColor,
                fontSize: calculatedFontSize,
                fontFamily: 'Roboto',
                fontWeight: FontWeight.w700,
                height: 1.0, // Set line height to 1.0 for better centering
                letterSpacing:
                    calculatedFontSize * 0.005, // letter-spacing: 0.5%
              ),
              textAlign: TextAlign.center,
              textHeightBehavior: const TextHeightBehavior(
                  applyHeightToFirstAscent: false,
                  applyHeightToLastDescent: false),
            ),
          ),
        ),
      );
    }

    // For user badges, make them circular with white background and black border
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle, // Make it circular
        border: borderColor != null
            ? Border.all(color: borderColor, width: calculatedBorderWidth)
            : null,
      ),
      child: Center(
        child: Text(
          levelText,
          style: TextStyle(
            color: textColor,
            fontSize: calculatedFontSize,
            fontFamily: 'Roboto',
            fontWeight: FontWeight.w700,
            letterSpacing: calculatedFontSize * 0.005,
            height: 1.0, // Set line height to 1.0 for better centering
          ),
          textAlign: TextAlign.center,
          textHeightBehavior: const TextHeightBehavior(
            applyHeightToFirstAscent: false,
            applyHeightToLastDescent: false,
          ),
        ),
      ),
    );
  }
}
