import 'package:flutter/cupertino.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class RefreshScreen extends StatefulWidget {
  final dynamic onTapRefreshFunction;
  const RefreshScreen({Key? key, this.onTapRefreshFunction}) : super(key: key);

  @override
  State<RefreshScreen> createState() => _RefreshScreenState();
}

class _RefreshScreenState extends State<RefreshScreen> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region refresh
  Widget body() {
    return SizedBox(
      height: double.infinity,
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 30),
            child: Text(
              AppStrings.somethingWentWrong,
              style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
            ),
          ),
          Container(
            alignment: Alignment.center,
            child: CupertinoButton(
              color: AppColors.brandBlack,
              onPressed: () {
                widget.onTapRefreshFunction();
              },
              child: Text(
                AppStrings.retry,
                style: AppTextStyle.heading2Bold(textColor: AppColors.appWhite),
              ),
            ),
          ),
        ],
      ),
    );
  }
//endregion
}
