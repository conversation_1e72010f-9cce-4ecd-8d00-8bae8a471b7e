import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding/seller_onboarding_screen.dart';
import 'package:swadesic/services/store_creation_auth/store_creation_auth_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum PhoneVerificationState {
  initial,
  loading,
  otpSent,
  verified,
  error,
}

enum ButtonState {
  normal,
  loading,
}

class PhoneVerificationBloc {
  // region Variables
  final BuildContext context;
  final String userReference;
  final bool isTestStore;

  // Controllers
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController otpController = TextEditingController();

  // Stream controllers
  final StreamController<PhoneVerificationState> stateController =
      StreamController<PhoneVerificationState>.broadcast();
  final StreamController<bool> otpSentController =
      StreamController<bool>.broadcast();
  final StreamController<bool> canResendController =
      StreamController<bool>.broadcast();
  final StreamController<ButtonState> buttonStateController =
      StreamController<ButtonState>.broadcast();
  final StreamController<bool> otpValidController =
      StreamController<bool>.broadcast();

  // Services
  final StoreCreationAuthService storeCreationAuthService =
      StoreCreationAuthService();
  // endregion

  // region Constructor
  PhoneVerificationBloc(this.context, this.userReference, this.isTestStore);
  // endregion

  // region Init
  void init() {
    // Get user's phone number if available
    final loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    final userDetail = loggedInUserInfoDataModel.userDetail;

    if (userDetail != null &&
        userDetail.phonenumber != null &&
        userDetail.phonenumber!.isNotEmpty) {
      // Format the phone number to remove country code if present
      String phoneNumber = userDetail.phonenumber!;
      if (phoneNumber.startsWith('+91')) {
        phoneNumber = phoneNumber.substring(3);
      }
      phoneNumberController.text = phoneNumber;
    }

    // Initialize stream values
    otpSentController.sink.add(false);
    canResendController.sink.add(true); // Allow resend initially
    stateController.sink.add(PhoneVerificationState.initial);
    buttonStateController.sink.add(ButtonState.normal);
    otpValidController.sink.add(false); // OTP not valid initially
  }
  // endregion

  // region Send OTP
  Future<void> sendOtp() async {
    // Store context locally to avoid async gap issues
    final currentContext = context;

    // Validate phone number
    if (phoneNumberController.text.isEmpty ||
        phoneNumberController.text.length != 10) {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.enterValidNumber, currentContext);
      }
      return;
    }

    // Show loading state in button
    buttonStateController.sink.add(ButtonState.loading);

    try {
      // Format phone number with country code
      String formattedPhoneNumber = '+91${phoneNumberController.text}';

      // Call API to send OTP
      Map<String, dynamic> response =
          await storeCreationAuthService.sendStoreCreationOtp(
        userReference: userReference,
        phoneNumber: formattedPhoneNumber,
      );

      // Check if the context is still valid
      if (!currentContext.mounted) return;

      // Check if phone is already verified
      if (response.containsKey('is_custom') &&
          response['is_custom'] == true &&
          response.containsKey('message') &&
          response['message'] == 'Phone number already verified') {
        // Phone is already verified, proceed to store creation
        goToStoreOnboarding();
        return;
      }

      // OTP sent successfully
      otpSentController.sink.add(true);

      // Reset button state
      buttonStateController.sink.add(ButtonState.normal);

      // Show success message
      if (currentContext.mounted) {
        CommonMethods.toastMessage('OTP sent successfully', currentContext);
      }
    } catch (e) {
      // Check if the context is still valid
      if (!currentContext.mounted) return;

      // Reset button state
      buttonStateController.sink.add(ButtonState.normal);

      // Handle error
      CommonMethods.toastMessage(
          'Failed to send OTP. Please try again.', currentContext);
      debugPrint('Error sending OTP: $e');
    }
  }
  // endregion

  // region Verify OTP
  Future<void> verifyOtp() async {
    // Store context locally to avoid async gap issues
    final currentContext = context;

    // Validate OTP
    if (otpController.text.isEmpty || otpController.text.length != 6) {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(
            'Please enter a valid 6-digit OTP', currentContext);
      }
      return;
    }

    // Show loading state in button
    buttonStateController.sink.add(ButtonState.loading);

    try {
      // Format phone number with country code
      String formattedPhoneNumber = '+91${phoneNumberController.text}';

      // Call API to verify OTP
      Map<String, dynamic> response =
          await storeCreationAuthService.verifyStoreCreationOtp(
        userReference: userReference,
        phoneNumber: formattedPhoneNumber,
        otp: otpController.text,
      );

      // Check if the context is still valid
      if (!currentContext.mounted) return;

      // Check if verification was successful
      if (response.containsKey('message') &&
          response['message'] == 'OTP verified successfully') {
        // OTP verified successfully

        // Reset button state
        buttonStateController.sink.add(ButtonState.normal);

        // Update the user model with verified phone number
        try {
          // Get the LoggedInUserInfoDataModel
          final loggedInUserInfoDataModel =
              Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

          // Update the phone number and verification status
          if (loggedInUserInfoDataModel.userDetail != null) {
            loggedInUserInfoDataModel.userDetail!.phonenumber =
                phoneNumberController.text;
            loggedInUserInfoDataModel.userDetail!.isPhonenumberVerified = true;

            // Notify listeners to update the UI
            loggedInUserInfoDataModel.updateUi();

            // Also update the AppConstants if needed
            if (AppConstants.appData.userReference == userReference) {
              AppConstants.appData.mobileNumber = phoneNumberController.text;
            }

            debugPrint(
                "Updated user model with verified phone number: ${phoneNumberController.text}");
          }
        } catch (e) {
          debugPrint("Error updating user model: $e");
        }

        // Show success message
        if (currentContext.mounted) {
          CommonMethods.toastMessage(
              'Phone number verified successfully', currentContext);
        }

        // Navigate to store onboarding
        if (currentContext.mounted) {
          goToStoreOnboarding();
        }
      } else {
        // Verification failed

        // Reset button state
        buttonStateController.sink.add(ButtonState.normal);

        if (currentContext.mounted) {
          CommonMethods.toastMessage(
              'Invalid OTP. Please try again.', currentContext);
        }
      }
    } catch (e) {
      // Check if the context is still valid
      if (!currentContext.mounted) return;

      // Reset button state
      buttonStateController.sink.add(ButtonState.normal);

      // Handle error
      CommonMethods.toastMessage(
          'Failed to verify OTP. Please try again.', currentContext);
      debugPrint('Error verifying OTP: $e');
    }
  }
  // endregion

  // region Update OTP Valid State
  void updateOtpValidState(String otp) {
    // Check if OTP is valid (6 digits)
    final bool isValid = otp.length == 6;
    otpValidController.sink.add(isValid);
  }
  // endregion

  // region Go to Store Onboarding
  void goToStoreOnboarding() {
    // Store context locally
    final currentContext = context;

    // Check if context is still valid
    if (!currentContext.mounted) return;

    var screen = SellerOnBoardingScreen(isTestStore: isTestStore);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.pushReplacement(currentContext, route);
  }
  // endregion

  // region Dispose
  void dispose() {
    // Close controllers
    phoneNumberController.dispose();
    otpController.dispose();
    stateController.close();
    otpSentController.close();
    canResendController.close();
    buttonStateController.close();
    otpValidController.close();
  }
  // endregion
}
