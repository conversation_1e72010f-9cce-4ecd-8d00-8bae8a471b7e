import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:swadesic/services/web_upload_service.dart';
import 'package:swadesic/util/image_converter.dart';

/// A service for handling seller onboarding operations specifically for web platform
class WebSellerOnboardingService {
  final WebUploadService _webUploadService = WebUploadService();

  /// Upload store logo for web platform
  ///
  /// This method handles uploading a store logo image for web by using bytes instead of file paths
  Future<void> uploadStoreLogo({
    required String url,
    required String fileName,
    required Uint8List bytes,
    required String fileType,
  }) async {
    try {
      // Check if the image is a PNG and convert if needed
      if (fileType.toLowerCase().contains('png')) {
        // Convert PNG to JPEG to avoid server-side errors
        bytes = ImageConverter.convertPngToJpeg(bytes);
        
        // Update the filename to reflect the new format
        fileName = fileName.replaceAll(
            RegExp(r'\.png$', caseSensitive: false), '.jpg');
        
        debugPrint('Converted PNG to JPEG: $fileName');
      }
      
      // Upload the store logo
      await _webUploadService.uploadStoreLogo(
        url: url,
        fileName: fileName,
        bytes: bytes,
      );
    } catch (e) {
      debugPrint('Error uploading store logo: $e');
      rethrow;
    }
  }
}
