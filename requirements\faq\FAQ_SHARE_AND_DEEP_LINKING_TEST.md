# FAQ Share and Deep Linking Test Guide

## Overview
This document provides comprehensive testing instructions for the FAQ share functionality and deep linking features.

## Features Implemented

### 1. Share Functionality
- **Category Share**: Share button in app bar to share current FAQ category
- **Question Share**: Individual share buttons for each FAQ question
- **Integration**: Uses existing ShareWithImageScreen for consistent sharing experience

### 2. Deep Linking Behavior
- **Category Links**: Navigate to specific FAQ category
- **Question Links**: Navigate to specific question with auto-scroll and auto-expand
- **URL Format**: `/faq?category={categoryId}&question={questionId}`

## Testing Instructions

### Test 1: Share Category
1. Open FAQ screen
2. Navigate to any category (e.g., "Pricing")
3. Tap the share icon in the app bar
4. Verify share screen opens with:
   - URL: `/faq?category=pricing`
   - Message: "Check out these Pricing FAQs on Swadesic!"

### Test 2: Share Individual Question
1. Open FAQ screen
2. Navigate to any category
3. Tap the share icon next to any question
4. Verify share screen opens with:
   - URL: `/faq?category={categoryId}&question={questionId}`
   - Message: "Found this helpful FAQ: "{question}" on Swadesic!"

### Test 3: Deep Link to Category
1. Create a category link: `/faq?category=pricing`
2. Navigate using the link
3. Verify:
   - FAQ screen opens
   - "Pricing" category is automatically selected
   - Questions from pricing category are displayed

### Test 4: Deep Link to Specific Question
1. Create a question link: `/faq?category=general&question=general_trial`
2. Navigate using the link
3. Verify:
   - FAQ screen opens
   - "General" category is automatically selected
   - "Is there a free trial available?" question is automatically expanded
   - Screen scrolls to show the question
   - Answer is immediately visible

### Test 5: Invalid Deep Links
1. Test invalid category: `/faq?category=invalid`
2. Test invalid question: `/faq?category=general&question=invalid`
3. Verify:
   - FAQ screen opens normally
   - Falls back to first category
   - No errors or crashes

## URL Examples for Testing

### Category Links
```
/faq?category=general
/faq?category=pricing
/faq?category=dashboard
/faq?category=api
```

### Question Links
```
/faq?category=general&question=general_trial
/faq?category=general&question=general_plan_change
/faq?category=pricing&question=pricing_cost
/faq?category=pricing&question=pricing_discounts
/faq?category=api&question=api_documentation
/faq?category=api&question=api_rate_limit
```

## Expected Behavior Summary

### When Sharing Category:
1. Share button in app bar generates category-specific URL
2. Share screen opens with appropriate message
3. Generated link redirects to that specific category

### When Sharing Question:
1. Share button next to question generates question-specific URL
2. Share screen opens with question-specific message
3. Generated link redirects to the category containing that question
4. Target question is automatically expanded and scrolled into view

### When Following Deep Links:
1. **Category Link**: Opens FAQ screen → Selects target category
2. **Question Link**: Opens FAQ screen → Selects target category → Expands target question → Scrolls to question

## Implementation Details

### Share Integration
- Uses existing `ShareWithImageScreen` for consistency
- Leverages `CommonMethods.accessBottomSheet()` for modal presentation
- Generates appropriate URLs using `FaqNavigation` helper methods

### Deep Linking Integration
- Integrated with existing `HandleUrl` class
- Recognizes `/faq` path segments
- Parses query parameters for category and question IDs
- Handles invalid IDs gracefully

### Auto-Scroll Feature
- Calculates approximate scroll position based on question index
- Uses smooth animation (500ms duration)
- Only triggers when deep linking to specific questions

## Code Usage Examples

### Programmatic Navigation
```dart
// Navigate to category
FaqNavigation.navigateToFaqCategory(context, 'pricing');

// Navigate to specific question
FaqNavigation.navigateToFaqQuestion(context, 'general', 'general_trial');

// Generate shareable links
String categoryLink = FaqNavigation.generateCategoryLink('pricing');
String questionLink = FaqNavigation.generateQuestionLink('general', 'general_trial');
```

### URL Handling
```dart
// Handle FAQ URLs
Uri faqUri = Uri.parse('/faq?category=pricing&question=pricing_cost');
FaqNavigation.handleFaqLink(context, faqUri);
```

## Notes
- All IDs are case-sensitive
- URLs work on all platforms (iOS, Android, Web)
- Share functionality integrates with device's native sharing
- Deep linking works from external apps, web browsers, and internal navigation
- Auto-scroll is approximate and may need adjustment based on actual item heights
