import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/invalid/invalid_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/product_slug_service/product_slug_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class OnTapTag{

  BuildContext context;
  String tagData;

  OnTapTag(this.context,this.tagData){
    _handleTagTap();
  }

  //region Handle tag tap
  Future<void> _handleTagTap() async {
    // Check if it's a product mention (format: "@storeName's productName")
    if (tagData.contains("/")) {
      await _handleProductMention();
    } else {
      await getStoreAndUserReference();
    }
  }

  //region Handle product mention
  Future<void> _handleProductMention() async {
    try {
      // Parse the product mention format: "@storeName's productName"
      String cleanedTag = tagData.replaceFirst("@", "");
      List<String> parts = cleanedTag.split("/");

      if (parts.length != 2) {
        // Invalid format, fallback to regular handling
        await getStoreAndUserReference();
        return;
      }

      String storeName = parts[0];
      String productName = parts[1];

      // Get store reference first
      String storeReference = await StoreAndUserReferenceServices()
          .getStoreAndUserReferences(handleAndUserName: storeName);

      if (storeReference.isEmpty || !storeReference.startsWith("S")) {
        // Store not found, show invalid screen
        _showInvalidScreen();
        return;
      }

      // Get product reference using the product slug
      String? productReference = await ProductSlugService()
          .getProductReferenceFromSlug(
        storeReference: storeReference,
        productSlug: productName,
      );

      if (productReference != null) {
        // Navigate to product detail screen
        _navigateToProductDetail(productReference);
      } else {
        // Product not found, show invalid screen
        _showInvalidScreen();
      }

    } catch (error) {
      _showInvalidScreen();
    }
  }


  void _navigateToProductDetail(String productReference) {
    if (context.mounted) {
      final screen = BuyerViewSingleProductScreen(
        productReference: productReference,
      );
      final route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(context, route);
    }
  }

  void _showInvalidScreen() {
    final screen = const InvalidScreen();
    final route = MaterialPageRoute(builder: (context) => screen);
    if (context.mounted) {
      Navigator.push(context, route);
    }
  }
  //endregion


  //region Get Store and user reference
  Future<void> getStoreAndUserReference() async {

    StatefulWidget screen ;
    try {
      String reference = await StoreAndUserReferenceServices().getStoreAndUserReferences(handleAndUserName: tagData.replaceFirst("@", ""));

      //If reference is null
      if(reference ==""){
        _showInvalidScreen();
        return;
      }

      //If reference is U then add data to user reference
      if(reference.split("").first.contains("U")){
        screen = UserProfileScreen(userReference:reference,);
        var route = MaterialPageRoute(builder: (context) => screen);
        context.mounted?Navigator.push(context, route):null;
        return;
      }
      //If reference is S then add data to user reference
      if(reference.split("").first.contains("S")){
        screen = BuyerViewStoreScreen(storeReference:reference,isStoreOwnerView:AppConstants.appData.storeReference==null?false:AppConstants.appData.storeReference! == reference);
        var route = MaterialPageRoute(builder: (context) => screen);
        context.mounted?Navigator.push(context, route):null;
        return;
      }
      // if(reference.isEmpty){
      //
      // }


    } on ApiErrorResponseMessage catch(error) {
      CommonMethods.toastMessage(error.message.toString(),AppConstants.userStoreCommonBottomNavigationContext);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage,AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
//endregion

}