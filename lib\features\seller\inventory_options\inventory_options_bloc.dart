import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/inventory_options/add_option_bottom_sheet.dart';
import 'package:swadesic/features/seller/inventory_options/add_variant_bottom_sheet.dart';
import 'package:swadesic/model/product_option/product_option.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/product_variant_service/product_variant_service.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/app_strings.dart';

enum InventoryOptionsState { Loading, Success, Failed, Empty }

class InventoryOptionsBloc {
  BuildContext context;
  final String storeReference;
  final Product product;

  // State variables
  bool hasOptions = false;
  List<ProductOption> productOptions = [];
  List<ProductVariant> productVariants = [];

  // Services
  late ProductVariantService productVariantService;

  // Controllers
  final refreshCtrl = StreamController<bool>.broadcast();
  final stateCtrl = StreamController<InventoryOptionsState>.broadcast();

  InventoryOptionsBloc(this.context, this.storeReference, this.product) {
    productVariantService = ProductVariantService();
  }

  void init() {
    // Initialize with existing product options if available
    if (product.options != null && product.options!.isNotEmpty) {
      hasOptions = true;
      productOptions = product.options!.entries
          .map((entry) => ProductOption(
                optionName: entry.key,
                optionValues: entry.value,
              ))
          .toList();
      
      // Load existing variants if product has a reference
      if (product.productReference != null && product.productReference != "New") {
        loadProductVariants();
      }
    }
    refreshCtrl.sink.add(true);
  }

  void setHasOptions(bool value) {
    hasOptions = value;
    if (!hasOptions) {
      productOptions.clear();
      productVariants.clear();
    }
    refreshCtrl.sink.add(true);
  }

  void showAddOptionBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddOptionBottomSheet(
        onOptionAdded: (option) {
          addProductOption(option);
        },
      ),
    );
  }

  void showEditOptionBottomSheet(ProductOption option) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddOptionBottomSheet(
        existingOption: option,
        onOptionAdded: (updatedOption) {
          updateProductOption(option, updatedOption);
        },
        onOptionDeleted: () {
          deleteProductOption(option);
        },
      ),
    );
  }

  void showAddVariantBottomSheet() {
    if (productOptions.isEmpty) {
      CommonMethods.toastMessage(
        "Please add product options first",
        context,
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVariantBottomSheet(
        productOptions: productOptions,
        existingVariants: productVariants,
        onVariantAdded: (variant) {
          addProductVariant(variant);
        },
      ),
    );
  }

  void showEditVariantBottomSheet(ProductVariant variant) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVariantBottomSheet(
        productOptions: productOptions,
        existingVariants: productVariants,
        existingVariant: variant,
        onVariantAdded: (updatedVariant) {
          updateProductVariant(variant, updatedVariant);
        },
        onVariantDeleted: () {
          deleteProductVariant(variant);
        },
      ),
    );
  }

  void addProductOption(ProductOption option) {
    // Check if option name already exists
    if (productOptions.any((existing) => 
        existing.optionName.toLowerCase() == option.optionName.toLowerCase())) {
      CommonMethods.toastMessage(
        "Option with this name already exists",
        context,
      );
      return;
    }

    productOptions.add(option);
    refreshCtrl.sink.add(true);
  }

  void updateProductOption(ProductOption oldOption, ProductOption newOption) {
    final index = productOptions.indexOf(oldOption);
    if (index != -1) {
      productOptions[index] = newOption;
      
      // Update variants that use this option
      for (var variant in productVariants) {
        if (variant.combinations.containsKey(oldOption.optionName)) {
          final value = variant.combinations[oldOption.optionName];
          variant.combinations.remove(oldOption.optionName);
          if (newOption.optionValues.contains(value)) {
            variant.combinations[newOption.optionName] = value!;
          }
        }
      }
      
      refreshCtrl.sink.add(true);
    }
  }

  void deleteProductOption(ProductOption option) {
    productOptions.remove(option);
    
    // Remove variants that use this option
    productVariants.removeWhere((variant) => 
        variant.combinations.containsKey(option.optionName));
    
    refreshCtrl.sink.add(true);
  }

  void addProductVariant(ProductVariant variant) {
    // Check if variant with same combinations already exists
    if (productVariants.any((existing) => 
        existing.hasSameCombinations(variant.combinations))) {
      CommonMethods.toastMessage(
        "This variant already exists",
        context,
      );
      return;
    }

    productVariants.add(variant);
    refreshCtrl.sink.add(true);
  }

  void updateProductVariant(ProductVariant oldVariant, ProductVariant newVariant) {
    final index = productVariants.indexOf(oldVariant);
    if (index != -1) {
      productVariants[index] = newVariant;
      refreshCtrl.sink.add(true);
    }
  }

  void deleteProductVariant(ProductVariant variant) {
    productVariants.remove(variant);
    refreshCtrl.sink.add(true);
  }

  Future<void> loadProductVariants() async {
    try {
      stateCtrl.sink.add(InventoryOptionsState.Loading);
      final response = await productVariantService.getProductVariants(
        productReference: product.productReference!,
      );
      productVariants = response.productVariants;
      stateCtrl.sink.add(InventoryOptionsState.Success);
      refreshCtrl.sink.add(true);
    } catch (e) {
      stateCtrl.sink.add(InventoryOptionsState.Failed);
      CommonMethods.toastMessage(
        "Failed to load variants: ${e.toString()}",
        context,
      );
    }
  }

  void onTapSave() {
    // Update product options
    if (hasOptions && productOptions.isNotEmpty) {
      product.options = {};
      for (var option in productOptions) {
        product.options![option.optionName] = option.optionValues;
      }
    } else {
      product.options = null;
    }

    // Close screen and return updated product
    Navigator.pop(context, product);
  }

  void dispose() {
    refreshCtrl.close();
    stateCtrl.close();
  }
}
