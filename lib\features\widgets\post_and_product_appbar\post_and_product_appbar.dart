import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';

class PostAndProductAppBar extends StatefulWidget {
  final Function onTapProfileImage;
  final Function onTapOptions;
  final String? icon;
  final String title;
  final Widget? customTitle;
  final String subTitle;
  final Widget? customSubTitle;
  final String data;
  final String entityType;
  final Widget? verifiedWidget;
  final String? level; // Optional level parameter for showing level badge
  const PostAndProductAppBar(
      {super.key,
      required this.onTapProfileImage,
      required this.icon,
      required this.title,
      required this.subTitle,
      this.data = "",
      required this.entityType,
      required this.onTapOptions,
      this.customTitle,
      this.customSubTitle,
      this.verifiedWidget,
      this.level});

  @override
  State<PostAndProductAppBar> createState() => _PostAndProductAppBarState();
}

class _PostAndProductAppBarState extends State<PostAndProductAppBar> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          //Icon
          InkWell(
            onTap: () {
              widget.onTapProfileImage();
            },
            child: CustomImageContainer(
              width: 36,
              height: 36,
              imageUrl: widget.icon,
              imageType: widget.entityType == EntityType.USER.name
                  ? CustomImageContainerType.user
                  : CustomImageContainerType.store,
              level: widget.level,
              showLevelBadge: widget.level != null,
              userBadgeBorderWidth: 0.9,
              userBadgeFontSize: 9,
              storeBadgeBorderWidth: 1.2,
              storeBadgeFontSize: 7,
              badgeWidth: 15,
              badgeHeight: 15,
              // Pass the level to show badge
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          //Title sub title and Date
          Expanded(
            child: GestureDetector(
              onTap: () {
                widget.onTapProfileImage();
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      widget.customTitle ??
                          Text(
                            widget.title,
                            style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.appBlack)
                                .copyWith(height: 0),
                            overflow: TextOverflow.ellipsis,
                          ),
                      widget.verifiedWidget ?? const SizedBox()
                    ],
                  ),

                  const SizedBox(
                    height: 5,
                  ),
                  //Sub title
                  widget.customSubTitle ??
                      Visibility(
                        visible: widget.subTitle.isNotEmpty,
                        child: Text(
                          widget.subTitle,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.smallText(
                                  textColor: AppColors.writingBlack1)
                              .copyWith(height: 0),
                        ),
                      ),
                ],
              ),
            ),
          ),
          //Three dots
          SizedBox(
              height: 18,
              width: 18,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  widget.onTapOptions();
                },
                child: RotatedBox(
                    quarterTurns: 1,
                    child: SvgPicture.asset(
                      AppImages.commentOption,
                      color: AppColors.appBlack,
                      height: 18,
                    )),
              ))
        ],
      ),
    );
  }
//endregion
}
