import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProfileImageView extends StatefulWidget {
  final String? imageUrl;
  const ProfileImageView({super.key, required this.imageUrl});

  @override
  State<ProfileImageView> createState() => _ProfileImageViewState();
}

class _ProfileImageViewState extends State<ProfileImageView> {
  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region Body
Widget body(){
    return Stack(
      alignment: Alignment.center,
      children: [
        GestureDetector(
          onTap: (){
            Navigator.pop(context);
          },
          child: Opacity(
            opacity: 0.5,
            child: Container(
              color: AppColors.appBlack,
              height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            ),
          ),
        ),
        Positioned(
            top: 0,
            right: 0,
            child: CupertinoButton(
                onPressed: (){
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  height: 30,
                  AppImages.close,color:AppColors.appWhite,))),
        Container(
          height: MediaQuery.of(context).size.width * 0.8,
          width: MediaQuery.of(context).size.width * 0.8,
          color: Colors.orange,
          child: extendedImage("${widget.imageUrl}", context, 800, 800,fit: BoxFit.contain)

          ),
      ],
    );
}
//endregion

}
