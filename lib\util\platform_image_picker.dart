import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/web_file_picker.dart';

/// Result of picking an image, containing all necessary data for both platforms
class PlatformImageResult {
  final File? file;         // For mobile
  final Uint8List? bytes;   // For web
  final String fileName;
  final String? mimeType;

  PlatformImageResult({
    this.file,
    this.bytes,
    required this.fileName,
    this.mimeType,
  });

  bool get isEmpty => (file == null && bytes == null);
  bool get isNotEmpty => !isEmpty;
}

/// A class that provides platform-agnostic image picking functionality
class PlatformImagePicker {

  /// Pick a single image from gallery
  ///
  /// Returns a PlatformImageResult that works for both web and mobile
  static Future<PlatformImageResult?> pickImage({
    ImageSource source = ImageSource.gallery,
    double? maxWidth,
    double? maxHeight,
    bool compress = true,
  }) async {
    try {
      if (kIsWeb) {
        // Web implementation
        final result = await WebFilePicker.pickImage();
        if (result == null) return null;

        return PlatformImageResult(
          bytes: result['bytes'] as Uint8List,
          fileName: result['name'] as String,
          mimeType: result['type'] as String,
        );
      } else {
        // Mobile implementation
        final picker = ImagePicker();
        final XFile? pickedFile = await picker.pickImage(
          source: source,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
        );

        if (pickedFile == null) return null;

        File imageFile = File(pickedFile.path);

        // Compress the image if needed
        if (compress) {
          imageFile = await CommonMethods.compressImage(originalFile: imageFile);
        }

        return PlatformImageResult(
          file: imageFile,
          fileName: imageFile.path.split('/').last,
        );
      }
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error picking image: $e');
      return null;
    }
  }

  /// Pick multiple images from gallery
  ///
  /// Returns a list of PlatformImageResult that works for both web and mobile
  static Future<List<PlatformImageResult>?> pickMultipleImages({
    double? maxWidth,
    double? maxHeight,
    bool compress = true,
  }) async {
    try {
      if (kIsWeb) {
        // Web implementation
        final results = await WebFilePicker.pickMultipleImages();
        if (results == null || results.isEmpty) return null;

        return results.map((result) => PlatformImageResult(
          bytes: result['bytes'] as Uint8List,
          fileName: result['name'] as String,
          mimeType: result['type'] as String,
        )).toList();
      } else {
        // Mobile implementation
        final picker = ImagePicker();
        final List<XFile> pickedFiles = await picker.pickMultiImage(
          maxWidth: maxWidth,
          maxHeight: maxHeight,
        );

        if (pickedFiles.isEmpty) return null;

        final results = <PlatformImageResult>[];

        for (final pickedFile in pickedFiles) {
          File imageFile = File(pickedFile.path);

          // Compress the image if needed
          if (compress) {
            imageFile = await CommonMethods.compressImage(originalFile: imageFile);
          }

          results.add(PlatformImageResult(
            file: imageFile,
            fileName: imageFile.path.split('/').last,
          ));
        }

        return results;
      }
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error picking multiple images: $e');
      return null;
    }
  }

  /// Crop an image
  ///
  /// For mobile, it uses the image_cropper package
  /// For web, it returns the original image (cropping not implemented for web yet)
  static Future<PlatformImageResult?> cropImage(PlatformImageResult image) async {
    if (kIsWeb) {
      // Web implementation - currently just returns the original image
      // TODO: Implement web cropping if needed
      return image;
    } else {
      // Mobile implementation
      if (image.file == null) return image;

      final croppedFile = await CommonMethods.imageCrop(file: image.file!);
      if (croppedFile == null) return null;

      return PlatformImageResult(
        file: croppedFile,
        fileName: croppedFile.path.split('/').last,
      );
    }
  }
}
