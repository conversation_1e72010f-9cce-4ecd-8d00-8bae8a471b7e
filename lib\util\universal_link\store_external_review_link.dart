import 'package:flutter/material.dart';
import 'package:swadesic/features/store_external_reviews/store_external_review_request_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreExternalReviewLink {
  String token;
  String storeReference;
  String userReference;

  StoreExternalReviewLink(this.token, {this.storeReference = '', this.userReference = ''}) {
    action();
  }

  // region Action
  void action() {
    // If storeReference or userReference is empty, get them from the token
    if (storeReference.isEmpty || userReference.isEmpty) {
      // In a real implementation, you would fetch these from the API
      // For now, we'll use default values
      storeReference = storeReference.isEmpty ? 'S1744452089786' : storeReference;
      userReference = userReference.isEmpty ? AppConstants.staticUser : userReference;
    }

    // Navigate to the store external review request screen
    var screen = StoreExternalReviewRequestScreen(
      token: token,
      storeReference: storeReference,
      userReference: userReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
  // endregion
}
