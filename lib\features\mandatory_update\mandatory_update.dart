import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:url_launcher/url_launcher.dart';

class MandatoryUpdate extends StatefulWidget {
  const MandatoryUpdate({super.key});

  @override
  State<MandatoryUpdate> createState() => _MandatoryUpdateState();
}

class _MandatoryUpdateState extends State<MandatoryUpdate> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
          height: MediaQuery.of(context).size.height,
          width: CommonMethods.calculateWebWidth(context: context),
          // decoration: const BoxDecoration(
          //   gradient: LinearGradient(
          //     begin: Alignment(0.64, -0.942),
          //     end: Alignment(-0.878, 1),
          //     colors: <Color>[
          //       Color(0x3310c057),
          //       Color(0x2c3ee07f),
          //       Color(0x3302d256)
          //     ],
          //     stops: <double>[0, 0.635, 0.927],
          //   ),
          // ),
          child: body()),
    );
  }

  //region Body
  Widget body() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 40),
        child: Column(
          children: [
            bharatFlag(),
            title(),
            subTitleAndButton(),
            updateButton(),
            message(),
          ],
        ),
      ),
    );
    // return Stack(
    //   children: [
    //     AnimatedContainer(
    //       duration:Duration(seconds: 2) ,
    //       height: 200,width: 200,color: Colors.green,),
    //     Positioned.fill(child: BackdropFilter(
    //       filter: ImageFilter.blur(sigmaX: 20,sigmaY: 30),
    //       child: const SizedBox(),
    //     ))
    //
    //   ],
    // );
  }

//endregion

  //region Bharat flag
  Widget bharatFlag() {
    return Image.asset(
      AppImages.appIcon,
      height: MediaQuery.of(context).size.height * 0.2,
      width: MediaQuery.of(context).size.height * 0.3,
    );
  }
  //endregion

//region Title
  Widget title() {
    return Text(
      "Namaskaram!",
      textAlign: TextAlign.center,
      style: AppTextStyle.introSlideTitle(textColor: AppColors.brandBlack),
    );
  }
//endregion

//region Sub title and button
  Widget subTitleAndButton() {
    return Expanded(
      child: Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "We have upgraded your Swadeshi experience with some mandatory changes, please update the app.",
              textAlign: TextAlign.center,
              style: AppTextStyle.updateApp(textColor: AppColors.writingBlack0),
            ),
            InkWell(
              onTap: () {
                var screen = AppWebView(url: AppConstants.whatsNew);
                var route = MaterialPageRoute(builder: (context) => screen);
                Navigator.push(context, route);
              },
              child: Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  child: Text(
                    "view changes",
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack, isUnderline: true),
                  )),
            )
          ],
        ),
      ),
    );
  }
//endregion

  //region Update button
  Widget updateButton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 25, left: 50, right: 50),
      child: AppCommonWidgets.activeButtonWithArrow(
        buttonName: 'Update Swadesic',
        onTap: () {
          launch(AppConstants.appPlayStoreLink);
        },
        buttonColor: AppColors.brandBlack,
      ),
    );
  }

  //endregion
//region Message
  Widget message() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Text(
        "We can’t let you in without the update as app may break at few places. Please update the app.\n- Team Swadesic",
        textAlign: TextAlign.center,
        style:
            AppTextStyle.smallTextRegular(textColor: AppColors.writingBlack0),
      ),
    );
  }
//endregion
}