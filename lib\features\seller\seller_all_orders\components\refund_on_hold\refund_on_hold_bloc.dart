import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/sellerNeedResolution/seller_need_resolution.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/seller_all_order_service/seller_all_order_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class RefundOnHoldBloc {
  // region Common Variables
  BuildContext context;
  late SellerAllOrderServices sellerAllOrderServices;
  final SellerSubOrderBloc sellerSubOrderBloc;
  final Order order;
  final List<SubOrder> subOrderList;
  List<String> groupNameList = [];

  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  final checkBoxCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  RefundOnHoldBloc(this.context, this.sellerSubOrderBloc, this.order, this.subOrderList);

  // endregion

  // region Init
  void init() {
    sellerAllOrderServices = SellerAllOrderServices();
    takeOutDisplayPackageNumbers();
  }
  // endregion

  //region Take out display package numbers
  void takeOutDisplayPackageNumbers() {
    //Clear the list
    groupNameList.clear();
    //Add all display package numbers
    for (var element in subOrderList) {
      if (!groupNameList.contains(element.displayPackageNumber)) {
        groupNameList.add(element.displayPackageNumber!);
      }
    }
  }
  //endregion

  // region On tap release refund
  Future onTapReleaseRefund({required SubOrder subOrder}) {
    return CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          onTapSave: (value) async {
            await releaseRefundAmountApi(subOrder: subOrder);
          },
          previousScreenContext: context,
          isMessageVisible: true,
          popPreviousScreen: false,
          message: AppStrings.areYouSureWantToReleaseHoldingAmount,
          firstButtonName: "Yes",
          secondButtonName: "No",
        )
    );
  }
  //endregion

  //region Release refund amount API
  Future<void> releaseRefundAmountApi({required SubOrder subOrder}) async {
    // Store context in local variable to avoid async gap issues
    final currentContext = context;

    try {
      await sellerAllOrderServices.releaseRefund(
        subOrderNumbers: [subOrder.suborderNumber!],
      );

      // Use the stored context
      if (currentContext.mounted) {
        //Message
        CommonMethods.toastMessage(AppStrings.amountIsReleased, currentContext);
      }

      //Get suborders
      sellerSubOrderBloc.getSubOrders();
    } on ApiErrorResponseMessage catch (error) {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(error.message ?? AppStrings.commonErrorMessage, currentContext);
      }
    } catch (error) {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      }
    }
  }
  //endregion

  // region On Tap need help
  Future onTapNeedHelp({required List<SubOrder> selectedSuborderList}) {
    return CommonMethods.appBottomSheet(
      context: context,
      screen: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        child: SellerNeedResolution(
          order: order,
          sellerSubOrderBloc: sellerSubOrderBloc,
          escalationReason: AppStrings.sellerRefundOnHoldEscalationReason,
          suborderList: selectedSuborderList,
        ),
      ),
      bottomSheetName: AppStrings.refundAmountIsOnHold,
    ).then((value) {
      // No additional actions needed after bottom sheet is closed
    });
  }
  // endregion
}
