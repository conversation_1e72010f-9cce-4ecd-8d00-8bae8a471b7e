import 'package:flutter/material.dart';
import 'package:swadesic/features/communication/welcome_store_screen.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class StoreCreationAnnouncement extends StatelessWidget {
  const StoreCreationAnnouncement({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(10),
      padding: const EdgeInsets.only(left: 10, right: 10, top: 20, bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Launch Your Store. Own Your Brand.',
            style: AppTextStyle.pageHeadingBold(textColor: AppColors.appBlack),
          ),
          const SizedBox(height: 5),

          // Description
          Text(
            'Set up your free store and go live with a built-in community. Swadesic manages payments, orders, and trust — so you can focus on growing your brand.',
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack2),
          ),
          const SizedBox(height: 15),

          // Button - smaller and left-aligned
          Align(
            alignment: Alignment.centerLeft,
            child: ElevatedButton(
              onPressed: () {
                // Create HomeAccessBloc to handle navigation
                final homeAccessBloc = HomeAccessBloc(context);

                // Get invite code (using empty string as placeholder)
                const inviteCode = '';

                // Navigate to WelcomeStoreScreen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WelcomeStoreScreen(
                      inviteCode: inviteCode,
                      homeAccessBloc: homeAccessBloc,
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(100),
                ),
              ),
              child: Text(
                'Create Store',
                style: AppTextStyle.access0(textColor: AppColors.appWhite),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
