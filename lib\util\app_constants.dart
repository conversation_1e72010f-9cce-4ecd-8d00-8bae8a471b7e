import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:persistent_bottom_nav_bar/persistent_tab_view.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/model/app_data/app_data.dart';

// import 'package:persistent_bottom_nav_barnav_bar/persistent-tab-view.dart';
///App state
enum AppState {
  Loading,
  Success,
  Failed,
  Empty,
  SearchEmpty,
  Deleted,
  InActive
}

//Banner enum
enum BannerEnum { FEED, HOME, STORE, USER }

class AppConstants {
  // static int userId = 0;
  static const channel = MethodChannel('com.sociallyx');
  static const setupReferral = "setupReferral";
  static const getReferralUrl = "getReferralUrl";
  //region Google clint id
  static const String devGoogleClintId =
      '723069619392-r0mbc01u9jl3k2duejhemkdgbsdffnnr.apps.googleusercontent.com';
  static const String prodGoogleClintId =
      '723069619392-ijsol1fjh9pfqr794cc8ld3bh2bmm0lp.apps.googleusercontent.com';
  //endregion

  ///Navigation context
  //region Navigation context
  static GlobalKey<NavigatorState> globalNavigator =
      GlobalKey<NavigatorState>();
  static late BuildContext userStoreCommonBottomNavigationContext;
  static late BuildContext currentSelectedTabContext;
  static late BuildContext appContext;
  static bool isSignInScreenOpenedForStatisUser = false;
  static String webChangedUrl = "";

  static Uri recentlyOpenedUrl = Uri();

  // Parameters for returning to external review screen after sign-in
  static Map<String, String> externalReviewParams = {};
  //endregion

  //region Referral code
  // static String referralCode = '';
  //endregion
  //region App media query height and width
  static double appHeight = 0.0;
  static double appWidth = 0.0;
  //endregion

  //region Links
  static String domainName = "https://swadesic.com/";
  static String prodWebsite = "https://swadesic.sociallyx.com/";
  static String devWebsite = "https://swadesic.sociallyx.com/";
  static String termsWebsite = "https://swadesic.sociallyx.com/policies/terms";
  static String devTermsWebsite =
      "https://swadesic.sociallyx.com/policies/terms";
  static String privacyPolityWebsite =
      "https://swadesic.sociallyx.com/policies/privacy-policy";
  static String devPrivacyPolityWebsite =
      "https://swadesic.sociallyx.com/policies/privacy-policy";
  static const appPlayStoreLink =
      "https://play.google.com/store/apps/details?id=com.sociallyx.swadesic";

  static String appPrivacyAndPolicy =
      "https://swadesic.sociallyx.com/policies/privacy-policy";
  static String appTermsAndConditionBuyer =
      "https://swadesic.sociallyx.com/policies/terms";
  static String appTermsAndConditionSeller =
      "https://swadesic.sociallyx.com/policies/seller-terms";
  static String whatsNew =
      "https://github.com/Socially-X/public_assets/blob/MASTER/whats_new.md";
  static String supportScoreMapUrl =
      "https://swadesic.sociallyx.com/help/swdesic-support-score";
  static String storeLevelUpUrl =
      "https://swadesic.sociallyx.com/help/swadesic-store-valuation";
  static String googleMapsHelpUrl =
      "https://support.google.com/maps/answer/18539?hl=en&co=GENIE.Platform%3DAndroid";
  //endregion
  ///AppData
  static AppData appData = AppData(isUserView: false, isStoreView: false);
  // static String? userReference;
  // static String? userName;

  static String mobileNumber = "";
  static String appVersion = "";

  // static  List<String> followingUserReferenceList = [];
  // static String buyerSearchFilter = 'all';
  // static int searchScreenSelectedTab =0;

  // static String storeReference = '';
  // static String productReference = '';
  static int userMemberRank = 0;
  // static String userMembershipType = "";

  //region Access controller
  static String createStore = '';

  // static bool isKeyboardVisible = false;
  //endregion
  //region Support
  // static bool isFeedbackReport = false;

  //endregion

  ///Global controller
  //region Global Controller
  static final bottomNavigationRefreshCtrl = StreamController<bool>.broadcast();
  // Global stream for product refresh notifications
  static final productRefreshCtrl =
      StreamController<Map<String, dynamic>>.broadcast();
  //User
  static PersistentTabController userPersistentTabController =
      PersistentTabController(initialIndex: 0);
  static PersistentTabController storePersistentTabController =
      PersistentTabController(initialIndex: 0);

  //Store

  // static final bottomNavigationCtrl = StreamController<bool>.broadcast();
//endregion
  ///Home screen and dashboard context
  static late BuildContext buyerHomeContext;
  // static late BuildContext dashBoardContext;

  // static ValueNotifier<bool> isBottomNavigationMounted = ValueNotifier<bool>(false);
  //region Universal link
  // static final isBottomNavigationMounted = ValueNotifier(false);
  static final ValueNotifier<bool> isBottomNavigationMounted =
      ValueNotifier<bool>(false);
  static bool isAppOpen = false;

  //endregion
  //endregion

  //region Admin access user list
  static const List<String> adminUserReference = [
    "U1711877682703",
    "U1711881079786",
    "U1711893323464",
    "U1711965860706",
    "S1711942497128"
  ];
  //endregion
  //region Non logged in user
  static const staticUser = 'U9999999999999';
  static const staticDevAccessToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxMzc2NjQ5LCJpYXQiOjE3NTA3NzE4NDksImp0aSI6IjBkNzIzNWM5MTA3YzRkNjhiZDUyOWYzNTdhNzc4NWI0IiwidXNlcl9pZCI6IlU5OTk5OTk5OTk5OTk5In0.RNAkjBlomyfVborF8mxx4g2RVsY57ZGxwMi2hXojYY4';
  static const staticProdAccessToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxMzc2OTU4LCJpYXQiOjE3NTA3NzIxNTgsImp0aSI6IjhiOThjYWJiNzgzNDQxY2JhYTVjYjBjZTg2YTdkMDJiIiwidXNlcl9pZCI6IlU5OTk5OTk5OTk5OTk5In0.zyCdtD5B0buw4AjnMHNPAXffHb2IJ1AFwd-IpaGIMM4';
  static const staticDevRefreshToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc4MjMwNzg0OSwiaWF0IjoxNzUwNzcxODQ5LCJqdGkiOiI4YjdhYWM3MWUwOWY0ZjJkOWE0MWI5NmRmYjFlODhmYyIsInVzZXJfaWQiOiJVOTk5OTk5OTk5OTk5OSJ9.GbD_POGwWa3-Hjqd0_KLch_BoYz9sNRuFgJnovinp-4';
  static const staticProdRefreshToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc4MjMwODE1OCwiaWF0IjoxNzUwNzcyMTU4LCJqdGkiOiI5ZGJmOTIxYzE3MWU0MmExYTQ4OGM1ZTRlZWEyYzM3NSIsInVzZXJfaWQiOiJVOTk5OTk5OTk5OTk5OSJ9.BxALXxSJTJTY9AfdROJEiSWsrSgLA1h8bn8_j2vfcWs';
  // static String messagingToken = '';
  // static String newMessagingToken = '';
  // static String newMessagingUserId = '';

  // static const staticUser = 'U1710262534082';
  // static const staticAccessToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzEzNjYzMzgzLCJpYXQiOjE3MTM1NzY5ODMsImp0aSI6IjU0ZjQyYjU2NWFhOTRjNDdiY2VkMjQ4MTYzYWViNmVjIiwidXNlcl9pZCI6IlUxNzEwMjYyNTM0MDgyIn0.zH-zJKMVQ0hAmWvUs5whaRquvzHJRyjtflriauX_kfk';
  static const staticAccessValidity = '2054-04-26T11:27:09.849346';
  // static const staticRefreshToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTcxNDE4MTc4MywiaWF0IjoxNzEzNTc2OTgzLCJqdGkiOiJiMmU4NjFhYTFhNDk0ZDY4YTI4MTExNmE5ZjI3OTkyMSIsInVzZXJfaWQiOiJVMTcxMDI2MjUzNDA4MiJ9.T4XROWZf33OuZiellU0oaPeNOVCfvoXqMHyTmSv8yH8';
  static const staticRefreshValidity = '2054-04-26T11:27:09.849346';

  //endregion
  ///Web view
  //region Flutter web incoming url
  static String webIncomingUrl = "";
  //endregion
  ///User level bottom navigation
  //region User and store level
  //Context

  //Save selected store reference
  // static late String selectedStoreReference = "";
  // static bool isSellerView = false;

  //endregion
  ///Bottom navigation data
  //region Bottom navigation Data
  // static int notificationCount = 0;
  static String bottomUserIcon = "";
  static String bottomStoreIcon = "";

  //endregion

  //region No internet
  static late BuildContext? noInternetDialogContext;
  static int numberOfBackButtonClickedInNoInternet = 0;

//endregion

  static bool isProfileComplete = false; //IT
  static const googleDriveBaseUrl =
      'https://docs.google.com/gview?embedded=true&url='; // New
  static const maintenanceUrl =
      'https://raw.githubusercontent.com/Socially-X/public_assets/MASTER/maintenance.json'; //
  static const myPlanFromGit =
      "https://raw.githubusercontent.com/Socially-X/public_assets/refs/heads/MASTER/my_plan.json";
  //region Payment options Screen
  static String htmlForm = "";
  static String orderNumber = "";
  static String txnToken = "";
  //endregion

  // static const baseUrl = 'https://whitelabel.onrender.com'; // SIT
  // static const baseUrl = 'https://2711-103-177-253-163.in.ngrok.io'; // SIT

  // static const baseUrl = 'http://216.48.190.154:8000'; // OLD
  // static const baseUrl = 'http://**************:8000'; // New
  static String baseUrl = ""; // New
  static String baseMediaUrl = "media/";
  static String baseMediaUrlWithslashes = "/media/";
  static String staticDevBaseUrl = "";
  static String staticProdBaseUrl = "";
  static String hmacKey = "123";
  static Environment appCurrentEnvironment = Environment.dev;
  static const apiTimeOutInSecond = 400;
  static String userPinCode = "$baseUrl/user/updatepincode/";
  static String sellerAddProduct = "$baseUrl/product/productlist/";
  static String userSignIn = "$baseUrl/user/userlogin/";
  static String newUserSignIn = "$baseUrl/user/newuserlogin/";
  static String signIn = "$baseUrl/user/signin/";
  static String checkUserInfo = "$baseUrl/user/check_user_info/";

  //region Email Authentication
  static String checkUserInfoByEmail = "$baseUrl/user/email/check_user_info/";
  static String emailSignIn = "$baseUrl/user/email/signin/";
  static String emailVerifyOtp = "$baseUrl/user/email/verify_signin/";
  static String emailResendOtp = "$baseUrl/user/email/resend_otp/";
  //endregion
  static String resendOtp = "$baseUrl/user/resend_otp/";
  static String verifyOtp = "$baseUrl/user/verify_signin/";
  static String checkOtp = "$baseUrl/user/checkotp/";
  static String newCheckOtp = "$baseUrl/user/newcheckotp/";
  static String sellerAddProductImage = "$baseUrl/brands";
  static String getSellerStoreProductList =
      "$baseUrl/store/getstoreproducts-seller/";
  static String getSellerStoreProductListNew = "$baseUrl/lean/store/";
  static String getProductFullDetails = "$baseUrl/lean/products/full_details/";
  static String getProductPartialDetails =
      "$baseUrl/lean/products/partial_details/";
  static String saveProduct = "$baseUrl/product/saveproduct/";
  static String checkProductSave =
      "$baseUrl/product/check_product_saved_or_not/";
  static String hideProduct = "$baseUrl/product/hideproducts/";
  static String unHideProduct = "$baseUrl/product/unhideproducts/";
  static String getStoreHiddenProductList =
      "$baseUrl/product/gethiddenproducts/";
  static String getEditProduct = "$baseUrl/product/productdetails/";
  static String updateStock = "$baseUrl/product/update_product_stock/";
  static String getProductImage = "$baseUrl/product/productimages/";
  static String deleteProduct = "$baseUrl/product/productdetails/";
  static String editProduct = "$baseUrl/productdetails/";
  static String visitNewStore = "$baseUrl/store/visitstores/";

  //region Buyer Home Screen
  static String getRecentVisitedStores = "$baseUrl/store/getrecentstores/";
  static String getFollowedStores = "$baseUrl/store/getfollowingstores/";
  static String deleteImage = "$baseUrl/product/productimagedetails/";

  //endregion

  //region Seller Home Screen
  static String sellerStoreList = "$baseUrl/store/get-userstores-list/";
  static String banner = "$baseUrl/general/banner_list/v2/";

  //endregion

  //region SellerOnBoarding
  static String getBusinessCategoryList = "$baseUrl/store/categorylist/";
  static String getBusinessBusinessType = "$baseUrl/store/business_type_list/";
  static String addBusinessCategoryList = "$baseUrl/store/categorylist/";
  static String addStore = "$baseUrl/store/storelist/";
  static String storeHandleAvailableCheck =
      "$baseUrl/store/storehandleavailability/";
  static String addDeleteEditStoreLink = "$baseUrl/store/storelink/";
  static String addStoreIcon = "$baseUrl/store/addstorelogo/";
  static String addStoreCoverImage = "$baseUrl/store/addstorecoverimage/";
  static String editStoreDetail = "$baseUrl/store/storedetails/";
  static String addAndEditStoreSignature = "$baseUrl/store/addstoresignature/";

  //endregion

  //region Product image re-order
  static String imageReorder = "$baseUrl/product/updateproductimageorder/";
  //endregion

  //region Buyer Search Screen
  static String getSearchedHistory = "$baseUrl/store/search_history/";
  static String addKeyWordHistory = "$baseUrl/store/add_search_history/";
  static String addSearchedItems = "$baseUrl/store/add_searched_item/";
  static String getSearch = "$baseUrl/store/search/";
  static String getStoreList = "$baseUrl/store/storelist/";
  static String getProductList = "$baseUrl/product/productlist/";
  static String removeSingleHistory =
      "$baseUrl/store/clear_one_search_history/";
  static String removeAllHistory = "$baseUrl/store/clear_search_history/";
  static String typingSuggestions = "$baseUrl/store/typing_suggestions/";

  //endregion

  //region Buyer Store View
  static String getStoreInfo = "$baseUrl/store/storedetails/";
  static String getStoreAndUserReferences =
      "$baseUrl/store/getting_reference_from_handle/";
  static String storeFollowStatus =
      "$baseUrl/store/check-store-followed-or-not/follower/";
  static String followUnfollow = "$baseUrl/store/follow-or-unfollow-store/";
  static String getBuyerStoreList = "$baseUrl/store/getstoreproducts-buyer/";
  static String addGst = "$baseUrl/store/storedetails/";
  static String storeContentSearch = "$baseUrl/store/entity/content_search/";
  static String getStoreMilestones = "$baseUrl/store/get_store_milestones/";

  //endregion

  //region User On Boarding
  static String userProfileCreate = "$baseUrl/user/userprofile";

  //endregion

  // region Comment Screen
  static String getProductComment = "$baseUrl/product/getcomment/";
  static String addParentComment = "$baseUrl/product/addcomment/";
  static String editParentComment = "$baseUrl/product/updatecommentsandreply/";
  static String editChildComment = "$baseUrl/product/updatecommentsandreply/";
  static String deleteParentComment = "$baseUrl/product/deletecomment/";
  static String deleteChildComment = "$baseUrl/product/deletereply/";
  static String addProductRating = "$baseUrl/product/updatecommentsandreply/";
  static String addChildComment = "$baseUrl/product/addreply/";
  static String addRemoveParentChildClap =
      "$baseUrl/product/addandremoveclaps/";
  static String addCommentImage =
      "$baseUrl/product/add_and_delete_review_image/0/";
  static String deleteCommentImage =
      "$baseUrl/product/add_and_delete_review_image/";
  static String checkReviewAccess = "$baseUrl/product/check_review_access/";
  static String getCommentOptions = "$baseUrl/graphdb/check_access/";

  //endregion

  //region Seller Trust Center
  static String getTrustCenterDetail = "$baseUrl/store/gettrustcenter/";
  static String addContactTrustCenter = "$baseUrl/store/addtrustcenter/";
  static String editContactTrustCenter =
      "$baseUrl/store/edit_trustcenter_contact/";
  static String editSwadeshiLabelTrustCenter =
      "$baseUrl/store/trustcenter_swadeshi_label/";
  static String addTrustCenterDocument = "$baseUrl/store/adddocument/";
  static String editTrustCenterDocument =
      "$baseUrl/store/edit_trustcenter_location/";
  static String getTrustCenterDocument = "$baseUrl/store/getdocument/";
  static String deleteTrustCenterDocument = "$baseUrl/store/deletedocument/";
  static String editTrustCenterDocumentName =
      "$baseUrl/store/editdocument_name/";
  static String editTrustCenterDocumentAndName = "$baseUrl/store/editdocument/";

  //endregion

  //region Delivery Settings
  static String addDeliveryStoreSetting = "$baseUrl/store/deliverysettings/";
  static String resetProductLevelDeliverySettings =
      "$baseUrl/store/reset_default_delivery_settings/";
  static String editDeliveryStoreSetting =
      "$baseUrl/store/deliverysettingsdetails/";
  static String getSellerStoreDeliverySettings =
      "$baseUrl/store/get_deliverysettings/";
  static String getSellerDeliveryPinCode = "$baseUrl/store/deliverylocations/";

  //endregion

  //region Logistic partners
  static String getLogisticsPartners = "$baseUrl/order/logistic_partner_list/";

  //endregion

  //region Return and warranty Settings
  static String getReturnWarrantySettings =
      "$baseUrl/store/get_refund_warranty/";
  static String getReturnWarrantyAddress =
      "$baseUrl/store/get_all_store_address/";
  static String addReturnWarrantyAddress =
      "$baseUrl/store/add_and_delete_address/0/";
  static String editReturnWarrantyAddress =
      "$baseUrl/store/add_and_delete_address/";
  static String deleteReturnWarrantyAddress =
      "$baseUrl/store/add_and_delete_address/";
  static String addReturnWarrantySetting =
      "$baseUrl/store/add_refund_warranty/";
  static String editReturnWarrantySetting =
      "$baseUrl/store/refund_warranty_updates/";
  static String resetToStoreReturnAndWarranty =
      "$baseUrl/store/reset_default_refund_warranty_settings/";

  // Return status constants
  static const String RETURN_REQUESTED = "RETURN_REQUESTED";
  static const String RETURN_CONFIRMED = "RETURN_CONFIRMED";
  static const String RETURN_SHIPPING_IN_PROGRESS =
      "RETURN_SHIPPING_IN_PROGRESS";
  static const String RETURN_RECEIVED = "RETURN_RECEIVED";
  static const String REFUND_ON_HOLD = "REFUND_ON_HOLD";
  static const String REFUND_PROCESSED = "REFUND_PROCESSED";

  //endregion

  //region Shopping cart
  static String addToCart = "$baseUrl/order/add_to_or_remove_from_cart/0/";
  static String deleteSingleCartItem =
      "$baseUrl/order/add_to_or_remove_from_cart/";
  static String getCartItems = "$baseUrl/order/get_cart_items/";
  static String getCartDetail = "$baseUrl/order/get_cart_details/";
  static String getCartPrice = "$baseUrl/order/delivery_fees_calculation/";
  static String updateCartProductQuantity =
      "$baseUrl/order/update_product_quantity/";
  static String shoppingCartTrustCenterDetail =
      "$baseUrl/get_store_and_trusrcenter_details_order_page/";
  static String orderCreate = "$baseUrl/order/create_delete_order/0/";
  static String createOrderAndInitiatePayment =
      "$baseUrl/order/create_order_and_initiate_payment/";
  static String paymentCreate = "$baseUrl/order/payment_creation/";
  static String getBankList = "$baseUrl/order/get_netbanking_payment_channels/";
  static String initiatePayment = "$baseUrl/order/initiate_razorpay_payment/";
  static String addToWishList = "$baseUrl/product/wishlist_product/";
  static String deleteMultipleCartItems = "$baseUrl/order/remove_cart_items/";
  static String productSlugCodeAvailability = "$baseUrl/product/product_slug_code_availability/";

  // static List<int> cartItemIdList = [];

  //endregion

  //region Single product api call
  static String singleProductDetail = "$baseUrl/product/productdetails/";
  static String singleProductImage = "$baseUrl/product/productimages/";
  static String singleProductDetailFromOrder =
      "$baseUrl/order/ordered_product_details/";

  //endregion

  //region Buyer and Seller Orders
  static String getSellerAllOrder = "$baseUrl/order/get_store_order_details/";
  static String getBuyerMyOrder = "$baseUrl/order/get_user_order_details/";
  static String buyerSellerStatus = "$baseUrl/order/update_order_status/";
  static String refundInitiateBySeller =
      "$baseUrl/order/refund_initiate_by_seller/";
  static String sellerAllOrderUserDetail =
      "$baseUrl/order/get_customer_details/";
  static String getOrderPaymentDetail = "$baseUrl/order/order_payment_details/";
  static String addDeleteGroup =
      "$baseUrl/order/update_and_delete_order_package/";
  static String deliveryTracking =
      "$baseUrl/order/update_delivery_tracking_details/";
  static String returnTracking =
      "$baseUrl/order/update_return_tracking_details/";
  static String sellerSendOtp = "$baseUrl/common/generate_otp/";
  static String sellerVerifyOtp = "$baseUrl/common/verify_otp/";
  static String getTrackingDetail =
      "$baseUrl/order/update_delivery_tracking_details/";
  static String needResolution = "$baseUrl/order/escalation/";
  static String getDisplayPackageNumber =
      "$baseUrl/order/create_display_package_number/";
  static String refundAmountCalculationBuyerView =
      "$baseUrl/order/check_possible_refund/";
  static String refundAmountCalculationSellerView =
      "$baseUrl/order/check_possible_refund/is_seller/";
  static String getOrderInvoice = "$baseUrl/order/invoice/?order_number";
  static String getPackageDetails = "$baseUrl/order/package-details/";
  static String generateShippingLabel =
      "$baseUrl/order/generate-shipping-label/";
  //endregion

  //region Upi payment
  static String getUpiIntentUrl = "$baseUrl/order/phonepe_intent_initiate/";
  static String collectUpiInitiate = "$baseUrl/order/phonepe_collect_initiate/";
  static String checkUpiValidation = "$baseUrl/order/phonepe_validate_vpa/";
  static String checkUpiPaymentStatus =
      "$baseUrl/order/phonepe_payment_status/";
  static String checkRazorPayPaymentStatus =
      "$baseUrl/order/check_razorpay_payment/";

  //endregion
  //region User Address
  static String getUserAddress = "$baseUrl/user/get_all_user_address/";
  static String addUserAddress = "$baseUrl/user/user_address_create/";
  static String editUserAddress = "$baseUrl/user/user_address_updates/";
  static String deleteUserAddress = "$baseUrl/user/user_address_updates/";

  //endregion

  //region Membership Screen
  static String friendsInvites = "$baseUrl/user/list_of_contact/";
  static String sentInvitation = "$baseUrl/user/list_of_invites_sent/";
  static String createInviteCode = "$baseUrl/user/create_invite_code/";
  static String deleteInviteCode = "$baseUrl/user/create_invite_code_details/";
  static String applyInviteCode = "$baseUrl/user/apply_invite_code/";

  //endregion

  //region Store Dashboard Screen
  static String storeOpenClose = "$baseUrl/store/open_or_close_store/";
  static String getStoreDashboard = "$baseUrl/store/get_store_dashboard/";
  static String deleteStore = "$baseUrl/store/delete_store/";
  static String activeDeactiveStore =
      "$baseUrl/store/activate_or_deactivate_store/";
  static String getStoreConfig = "$baseUrl/store/get_store_configs/";

  //endregion

  //region User detail
  static String getUserDetails = "$baseUrl/user/get_user_details/";
  static String getCity = "$baseUrl/store/city_list/";
  static String editUserDetail = "$baseUrl/user/userprofile/";
  static String updateUserProfilePic = "$baseUrl/user/user_profile_picture/";
  static String userFollowUnFollow = "$baseUrl/user/follow_unfollow_user/";
  static String checkUserNameAvailable =
      "$baseUrl/user/user_name_availability/";
  static String getUserContributions = "$baseUrl/user/get_user_contributions/";
  //endregion

  //region Payment api
  static String netBankingTransactionUrl =
      "$baseUrl/order/process_transaction/";
  static String cardTransactionUrl = "$baseUrl/order/process_transaction/";
  static String cardInfo = "$baseUrl/order/fetch_card_details/";
  static String upiTransactionUrl = "$baseUrl/order/process_transaction/";
  static String upiValidate = "$baseUrl/order/validate_vpa/";
  static String upiValidateCheck = "$baseUrl/order/phonepe_validate_vpa/";
  static String paymentStatusCheck = "$baseUrl/order/payment_status_varify/";
  //endregion

  //region Account balance
  static String accountBalance = "$baseUrl/order/get_account_balance/";
  static String getStoreTransactions = "$baseUrl/order/all_transactions_store/";
  static String getOrderTransactions = "$baseUrl/order/all_transactions_order/";
  static String accountBalancePayment =
      "$baseUrl/order/get_account_payout_details/";
  static String withDrawRequestOtp = "$baseUrl/order/send_payout_otp/";
  static String withDrawVerifyOtp = "$baseUrl/order/verify_payout_otp/";
  static String getSavedBankList = "$baseUrl/order/get-bank-details/";
  static String addBankDetail = "$baseUrl/order/add-bank-details/";
  static String markAsPrimaryAccount = "$baseUrl/order/set-primary-account/";
  static String deleteBankAccountDetail =
      "$baseUrl/order/delete-bank-details/?bank_detail_reference";

//endregion

//region Shipping history
  static String getShippingHistory = "$baseUrl/order/get_shipping_updates/";
  static String addShippingHistory = "$baseUrl/order/add_shipping_updates/";
  static String editDeleteHistory =
      "$baseUrl/order/edit_and_delete_shipping_updates/";

//endregion

//region Swadesic Shipping
  static String getPickupAddresses = "$baseUrl/store/get_all_store_address/";
  static String getShippingBalance = "$baseUrl/order/shipping-balance/";
  static String getAvailableCouriers = "$baseUrl/order/check-serviceability/";
  static String createShiprocketOrder =
      "$baseUrl/order/create-shiprocket-order/";

//endregion

//region Notification
  static String getAllUserNotification = "$baseUrl/user/all_notifications/";
  static String getAllStoresNotification =
      "$baseUrl/user/all_stores_notifications/";
  static String updateNotification =
      "$baseUrl/user/update_notification_status/";
  static String clearNotification = "$baseUrl/user/clear_all_notifications/";
//endregion

//region Feedback screen
  static String getAddAndReplayFeedback = "$baseUrl/store/feedback/";
  static String getFeedbackDetail = "$baseUrl/store/feedback_details/";
  static String feedbackImage = "$baseUrl/store/feedback_files/";
  static String feedbackVote = "$baseUrl/store/upvote/";
  static String updateFeedback = "$baseUrl/store/feedback_details/";

//endregion

//region Sub Order history
  static String getSubOrderHistory = "$baseUrl/order/get_suborder_history/";
//endregion

//region Bank account
  static String withdrawToBank = "$baseUrl/order/payout_withdrawal/";
//endregion

  //region Device id and device token
  String bearerToken = "";
  static String refreshToken = "$baseUrl/api/token/refresh/";
  static String checkUserDeviceDetail =
      "$baseUrl/user/check_user_device_entry/";
  static String userDeviceDetail = "$baseUrl/user/user_device_details/";
  //endregion

  //region Find your friends
  static String getUserAndStoreFromContactNumber =
      "$baseUrl/user/get_user_and_store_profiles/";
  static String followUnFollowUnRegisteredUser =
      "$baseUrl/user/follow_unregistered_user/";
  static String followAndUnFollowRegisterUserAndStore =
      "$baseUrl/user/follow_or_support_all/";

  //endregion

  //region Post
  static String getAllPost = "$baseUrl/content/get_all_posts/";
  static String getFeeds = "$baseUrl/content/get_feed_posts/";
  static String getSinglePost = "$baseUrl/content/get_single_post/";
  static String createPost = "$baseUrl/graphdb/create_post/";
  static String createComment = "$baseUrl/graphdb/add_comment/";
  static String editPost = "$baseUrl/graphdb/update_post/";
  static String editComment = "$baseUrl/graphdb/update_comment/";
  static String deletePost = "$baseUrl/content/post/";
  static String likeDisLikePost = "$baseUrl/content/like_unlike_post/";
  static String deletePostImage = "$baseUrl/content/delete_post_image/";
  static String getPostTaggedObjectDetails = "$baseUrl/graphdb/get_post_tagged_object_details/";

  //endregion

  //region Graph Ql
  static String graphQlUrl = "$baseUrl/graphDBql/";
  //endregion

  //region User rewards and invitees
  static String getUserReward = "$baseUrl/user/reward_data/";
  static String getInviteCode = "$baseUrl/user/get_invite_code/?reference=";
  static String getInviteRewardInfo = "$baseUrl/user/get_invite_rewards_info/";
  static String getSellerReward = "$baseUrl/store/store_rewards/";
  static String sellerMonthlyFlashPoint =
      "$baseUrl/store/store_monthly_flash_points/";
  static String getUserRewardTransaction = "$baseUrl/user/rewards_history/";
  static String getInviteeCount = "$baseUrl/user/invited_counts/";
  static String getSellerRewardTransaction =
      "$baseUrl/store/store_rewards_history/";
  //endregion

  //region AppConfig and meta data
  static String appConfig = "$baseUrl/common/app_configurations/";
  static String getBaseUrl = "https://baseurl.swadesic.com"; // New
  //endregion

  //region FAQ
  static String faqData = "$baseUrl/faq/data/";
  //endregion

  //region Affiliated program
  static String affiliatedProgram = "$baseUrl/user/get_user_affiliate_balance/";

  //endregion

  //region Messaging
  static String getAllMessages = "$baseUrl/messaging/get-chat-list/";
  static String getAllMessagesWebSocket =
      "${baseUrl.replaceFirst("https", "wss")}/messaging/ws?token="; //wss://e2e-77-175.ssdcloudindia.net/messaging/ws?token=
  static String getMessageDetail =
      "$baseUrl/messaging/api/get_dm_messages"; //https://e2e-77-175.ssdcloudindia.net/messaging/api/
  static String getDmMetaData =
      "$baseUrl/messaging/api/get_dm_metadata"; //https://e2e-77-175.ssdcloudindia.net/messaging/api/
  static String sendMessage = "$baseUrl/messaging/send-message/";
  static String messagingFileUploadEndpoint =
      '$baseUrl/messaging/api/files/upload';
  static String messagingFileViewingEndpoint =
      baseUrl.replaceFirst(RegExp(r'\/(dev|prod)$'), '') + '/messaging';
  //endregion

  //region New Messaging
  // REST API Base URL
  static String newMessaging_baseUrl = "";

  /// Get messaging base URL based on current environment
  static String getMessagingBaseUrl() {
    switch (appCurrentEnvironment) {
      case Environment.dev:
        return "http://**************:4000";
      case Environment.qa:
        return "http://**************:4000"; // Update with QA URL when available
      case Environment.prod:
        return "http://**************:4000"; // Update with Production URL when available
      case Environment.local:
        return "http://localhost:4000"; // Local development URL
      default:
        return "http://**************:4000"; // Default to dev
    }
  }

  // Users
  static String newMessaging_getAllUsers = "$newMessaging_baseUrl/api/users";
  static String newMessaging_getUserById =
      "$newMessaging_baseUrl/api/users/"; // append userId
  static String newMessaging_updateUser =
      "$newMessaging_baseUrl/api/users/"; // append userId
  static String newMessaging_deleteUser =
      "$newMessaging_baseUrl/api/users/"; // append userId

  // Chats
  static String newMessaging_getAllChats =
      "$newMessaging_baseUrl/api/chats/get_all_chats";
  static String newMessaging_getChatById =
      "$newMessaging_baseUrl/api/chats/get_chat_by_chatId/chatId="; // append chat_id
  static String newMessaging_createChat =
      "$newMessaging_baseUrl/api/chats/create_chat";
  static String newMessaging_checkDirectChat =
      "$newMessaging_baseUrl/api/chats/check_direct_chat/userId="; // append userId
  static String newMessaging_updateChat =
      "$newMessaging_baseUrl/api/chats/"; // append chatId
  static String newMessaging_deleteChat =
      "$newMessaging_baseUrl/api/chats/"; // append chatId

  // File Upload
  static String newMessaging_uploadFiles =
      "$newMessaging_baseUrl/api/files/upload";

  // Messages
  static String newMessaging_getMessagesFromSequence =
      "$newMessaging_baseUrl/api/messages/get_messages_from_sequence"; // append chatId, then add query params
  static String newMessaging_getMessageById =
      "$newMessaging_baseUrl/api/messages/get_message_by_messsageId/messageId="; // append messageId
  static String newMessaging_markAsRead =
      "$newMessaging_baseUrl/api/messages/mark_as_read";
  // WebSocket Connection
  static String newMessaging_webSocketConnection =
      "${newMessaging_baseUrl.replaceFirst("http", "ws")}/ws?token=";

  // External APIs (from Django project)
  static String newMessaging_getRecommendedContacts =
      "https://e2e-77-175.ssdcloudindia.net/dev/user/get_chat_entity_recommendations/"; //token should be AppConstants.appData.accessToken

  //region Subscription
  static String getStoreSubscription = "$baseUrl/subscriptions/store/";
  static String getUserSubscription = "$baseUrl/subscriptions/user/";
  static String createSubscription =
      "$baseUrl/subscriptions/create-subscription/";
  static String cancelSubscription =
      "$baseUrl/subscriptions/cancel-subscription/";
  static String getSubscriptionPaymentStatus =
      "$baseUrl/subscriptions/check-subscription-status/";

  //endregion

  //region Story
  static String addStory = "$baseUrl/content/story/";

  //endregion

  static List<XFile> multipleSelectedImage = [];
  static XFile? selectedSingleImage;
  static List<Map<String, dynamic>> webProductImages = [];
  // static List<String> selectedProducts = [];

  //region Regex
  static const onlyInt = "[0-9]";
  static const acceptAllWithoutSpace = r'^\S+$';
  static const onlyStringWithSpace = "[A-Z a-z]";
  static const onlyStringNoSpace = "[A-Za-z]";
  static const intStringWithSpace = "[a-z A-Z0-9]";
  static const intStringNoSpace = "[a-zA-Z0-9]";
  static const upiNameRegex = "[a-zA-Z0-9-.]";
  static const intCapitalSmallStringAndUnderScore = "[a-zA-Z0-9_]";
  static const acceptAll = "[^>]";
  static const panRegex = r'[A-Za-z]{5}[0-9]{4}[A-Za-z]{1}';
  static const gstRegex =
      r'\d{2}[A-Za-z]{5}\d{4}[A-Za-z]{1}[1-9A-Za-z]{1}[Zz]{1}[1-9A-Za-z]{1}';
  static const urlValidate =
      r"((https?:www\.)|(https?:\/\/)|(www\.))[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9]{1,6}(\/[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)?";
  // static var urlRegex = RegExp(r'(https?://)?([\da-zA-Z.?=&-]+)\.([a-zA-Z.]{2,6})([/\w.-]*)*\??', caseSensitive: false, multiLine: false);
  static var urlRegex = RegExp(
    r'\b(?:https?|http)?://[-A-Za-z0-9+&@#/%?=~_|!:,.;]*[-A-Za-z0-9+&@#/%=~_|]',
  );
  // static var urlRegex = RegExp(r'^[a-zA-Z0-9]+(\.[a-zA-Z]{2,})+(/[a-zA-Z0-9#]+)*$');

  // static var urlRegex = RegExp(r'\.(com|org|net|gov|edu|mil|int|biz|info|name|pro|coop|museum|aero|xxx|cat|jobs|travel|mobi|tel)(\/|$|\?)');
  // static var urlRegex = RegExp(r'^[^.]+(?:\.[^.]+)*\.[^.]+$',caseSensitive: true,multiLine: false);
  //  static var urlRegex = RegExp(r'^[^\s.]+(?:\.[^\s.]+)*\.(?:com|in|co)$');

  static const atTag = r"\B@[a-zA-Z0-9_/]+(?:'s\s+[a-zA-Z0-9\s\-_]+)*";

  static const mentionTag = r"\B\{\{mention:\{[^}]*\}\}\}";

  // static const atTag = r"\B@[^>]+\b";
  static const hashTag = r"\B#[a-zA-Z0-9]+\b";

// static const exclamenationTag = r"\B![a-zA-Z0-9]+\b";
// static const url = "(?:(?:https?|ftp):\/\/)?[\w/\-?=%.]+\.[\w/\-?=%.]+";
//endregion

  //region Waiting
  static const waitingForConfirmation = "WAITING_FOR_CONFIRMATION";
  static const subOrderPaymentSuccessStatus = "PAYMENT_SUCCESS";
  static const returnRequestedStatus = "RETURN_REQUESTED";

  //endregion

  //region Open
  static const subOrderConfirmedStatus = "ORDER_CONFIRMED";
  static const subOrderScheduledForShippingStatus = "SCHEDULED_FOR_PICKUP";
  static const deliveryInProgressStatus = "DELIVERY_IN_PROGRESS";
  static const scheduledForPickupStatus = "SCHEDULED_FOR_PICKUP";
  static const paymentPendingStatus = "PAYMENT_PENDING";
  static const escalatedStatus = "ORDER_ESCALATED";
  static const returnFailed = "RETURN_FAILED";
  static const refundHold = "REFUND_HOLD";

  //If buyer cancelled after shipping0
  //static const orderCanceledByBuyerStatus = "ORDER_CANCELLED_BY_BUYER";
  static const returnInProgress = "RETURN_IN_PROGRESS";
  static const returnRequestConfirmed = "RETURN_CONFIRMED";

  //endregion

  //region Closed
  static const orderDeliveredStatus = "ORDER_DELIVERED";
  static const orderCanceledBySellerStatus = "ORDER_CANCELLED_BY_SELLER";
  static const paymentFailedStatus = "PAYMENT_FAILED";

  //If buyer cancelled before shipping
  //static const orderCanceledByBuyerStatus = "ORDER_CANCELLED_BY_BUYER";
  static const orderAutoCancelled = "ORDER_AUTO_CANCELLED";
  static const returnedToSeller = "RETURNED_TO_SELLER";

  //endregion

  //region Refund
  static const refundInitiated = "REFUND_INITIATED";
  static const refunded = "REFUNDED";
  //endregion
//Not in use
  static const orderCanceledByBuyerStatus = "ORDER_CANCELLED_BY_BUYER";
  static const orderCancelledInTransit = "CANCELLED_IN_TRANSIT";

  ///Buyer
/*
2 waiting confirmation,5 cancelled ,3 confirmed, 1 shipped, 5 delivered,
3 returned  - consider this in same order statuses

//region waiting confirmation
  static const subOrderConfirmedStatus = "ORDER_CONFIRMED";
  static const deliveryInProgressStatus = "DELIVERY_IN_PROGRESS";
//endregion

//region cancelled
  static const orderCanceledBySellerStatus = "ORDER_CANCELLED_BY_SELLER";
  static const orderCanceledByBuyerStatus = "ORDER_CANCELLED_BY_BUYER";
//endregion

//region confirmed
  static const subOrderConfirmedStatus = "ORDER_CONFIRMED";
//endregion

//region shipped
  static const deliveryInProgressStatus = "DELIVERY_IN_PROGRESS";
//endregion

//region delivered
  static const orderDeliveredStatus = "ORDER_DELIVERED";
//endregion

//region Returning
 static const returnInProgress = "RETURN_IN_PROGRESS";
   static const returnRequestedStatus = "RETURN_REQUESTED";
  static const returnRequestConfirmed = "RETURN_CONFIRMED";
//endregion

//region returned
  static const returnedToSeller = "RETURNED_TO_SELLER";
//endregion

//region refunded
 amount refunded new flag.
//endregion



 */

  //Seller delivered
  static const amountReleased = "AMOUNT_RELEASED";

  //region Deep Link

  static const firebaseDeepLink =
      "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=";
  static const firebaseKey = "AIzaSyCIHdMjzgNFkJP9b--povHZ3QLjoxY9A8k";

//endregion

//region Font family
  static const rRegular = "RobotoRegular";
  static const rMedium = "RobotoMedium";
  static const rBold = "RobotoBold";
  static const rMBold = "RobotoMonoBold";
  static const cNeuwRegular = "cNeueRegular";
  static const leagueBlack = "LeagueBlack";
  static const leagueSemiBold = "LeagueSemiBold";
  static const leagueExtraBold = "LeagueExtraBold";
  static const leagueSpartan = "LeagueSpartan";

  ///
  static const cBold = "ComfortaaBold";
  static const lavishlyRegular = "LavishlyYoursRegular";
  static const poppinsSemiBold = "PoppinsSemiBold";
  static const poppinsBlack = "PoppinsBlack";
  static const poppinsExtraBold = "PoppinsExtraBold";
  static const frankRuhlLibre = "FrankRuhlLibre";

//endregion

  //region Indian States
  static const List<String> indianStates = [
    'Andhra Pradesh',
    'Arunachal Pradesh',
    'Assam',
    'Bihar',
    'Chattisgarh',
    'Goa',
    'Gujarat',
    'Haryana',
    'Himachal Pradesh',
    'Jharkhand',
    'Karnataka',
    'Kerala',
    'Madhya Pradesh',
    'Maharashtra',
    'Manipur',
    'Megalaya',
    'Mizoram',
    'Nagaland',
    'Odisha',
    'Punjab',
    'Rajasthan',
    'Sikkim',
    'Tamil Nadu',
    'Telangana',
    'Tripura',
    'Uttar Pradesh',
    'Uttarakhand',
    'West Bengal',
    'Andaman And Nicobar Islands',
    'Chandigarh',
    'Dadra And Nagar Haveli',
    'Daman And Diu',
    'Delhi',
    'Jammu And Kashmir',
    'Ladakh',
    'Lakshadweep',
    'Pondicherry',
  ];
  //endregion
}
