import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/your_invitees/your_invitees.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/account_balance/account_balance_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/seller_rewards/seller_rewards.dart';
import 'package:swadesic/features/widgets/app_left_align_tab_bar/app_left_align_tab_bar.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class SellerAccountBalanceAndRewards extends StatefulWidget {
  final int selectedTab;
  final String storeReference;
  const SellerAccountBalanceAndRewards({Key? key, required this.storeReference, this.selectedTab = 0,}) : super(key: key);

  @override
  _SellerAccountBalanceAndRewardsState createState() => _SellerAccountBalanceAndRewardsState();
}

class _SellerAccountBalanceAndRewardsState extends State<SellerAccountBalanceAndRewards> with SingleTickerProviderStateMixin{
  //Tab controller
  late TabController tabController;
  //region Init
  @override
  void initState() {
    tabController = TabController(length: 3, vsync: this);
    // Ensure selectedTab is within bounds (0 or 1)
    final validTab = widget.selectedTab >= 3 ? 0 : widget.selectedTab;
    tabController.animateTo(validTab);
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: TabBarView(
          controller: tabController,
          children: [
            AccountBalanceScreen(storeReference: widget.storeReference),
            SellerReward(),  
            YourInvitees()
          ]
      ),
    );
  }

  //region Appbar
  AppLeftAlignTabBar appBar() {
    return AppLeftAlignTabBar(
      title: "",
      tabController: tabController,
      tabs: [
        AppStrings.accountBalance,
        AppStrings.reward,  
        AppStrings.myInvitees
      ],
    );
  }
//endregion

}
