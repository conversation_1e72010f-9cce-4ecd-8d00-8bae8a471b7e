class ProductVariant {
  int? productVariantId;
  String? variantReference;
  String? productReference;
  Map<String, String> combinations;
  int mrpPrice;
  int sellingPrice;
  int stock;
  bool isActive;
  String? createdDate;
  String? modifiedDate;
  String? variantVersion;

  ProductVariant({
    this.productVariantId,
    this.variantReference,
    this.productReference,
    required this.combinations,
    required this.mrpPrice,
    required this.sellingPrice,
    required this.stock,
    this.isActive = true,
    this.createdDate,
    this.modifiedDate,
    this.variantVersion,
  });

  ProductVariant.fromJson(Map<String, dynamic> json)
      : productVariantId = json['product_variantid'],
        variantReference = json['variant_reference'],
        productReference = json['product_reference'],
        combinations = Map<String, String>.from(json['combinations'] ?? {}),
        mrpPrice = json['mrp_price'] ?? 0,
        sellingPrice = json['selling_price'] ?? 0,
        stock = json['stock'] ?? 0,
        isActive = json['is_active'] ?? true,
        createdDate = json['created_date'],
        modifiedDate = json['modified_date'],
        variantVersion = json['variant_version'];

  Map<String, dynamic> toJson() {
    return {
      'product_variantid': productVariantId,
      'variant_reference': variantReference,
      'product_reference': productReference,
      'combinations': combinations,
      'mrp_price': mrpPrice,
      'selling_price': sellingPrice,
      'stock': stock,
      'is_active': isActive,
      'created_date': createdDate,
      'modified_date': modifiedDate,
      'variant_version': variantVersion,
    };
  }

  ProductVariant copyWith({
    int? productVariantId,
    String? variantReference,
    String? productReference,
    Map<String, String>? combinations,
    int? mrpPrice,
    int? sellingPrice,
    int? stock,
    bool? isActive,
    String? createdDate,
    String? modifiedDate,
    String? variantVersion,
  }) {
    return ProductVariant(
      productVariantId: productVariantId ?? this.productVariantId,
      variantReference: variantReference ?? this.variantReference,
      productReference: productReference ?? this.productReference,
      combinations: combinations ?? this.combinations,
      mrpPrice: mrpPrice ?? this.mrpPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      stock: stock ?? this.stock,
      isActive: isActive ?? this.isActive,
      createdDate: createdDate ?? this.createdDate,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      variantVersion: variantVersion ?? this.variantVersion,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductVariant &&
        other.productVariantId == productVariantId &&
        other.variantReference == variantReference &&
        other.productReference == productReference &&
        _mapEquals(other.combinations, combinations) &&
        other.mrpPrice == mrpPrice &&
        other.sellingPrice == sellingPrice &&
        other.stock == stock &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return productVariantId.hashCode ^
        variantReference.hashCode ^
        productReference.hashCode ^
        combinations.hashCode ^
        mrpPrice.hashCode ^
        sellingPrice.hashCode ^
        stock.hashCode ^
        isActive.hashCode;
  }

  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true;
    for (final K key in a.keys) {
      if (!b.containsKey(key) || b[key] != a[key]) {
        return false;
      }
    }
    return true;
  }

  // Helper method to get variant display name
  String get displayName {
    return combinations.entries
        .map((entry) => '${entry.key}: ${entry.value}')
        .join(', ');
  }

  // Helper method to check if combinations match
  bool hasSameCombinations(Map<String, String> otherCombinations) {
    return _mapEquals(combinations, otherCombinations);
  }

  // Helper method to get discount percentage
  double get discountPercentage {
    if (mrpPrice <= 0) return 0.0;
    return ((mrpPrice - sellingPrice) / mrpPrice) * 100;
  }
}
