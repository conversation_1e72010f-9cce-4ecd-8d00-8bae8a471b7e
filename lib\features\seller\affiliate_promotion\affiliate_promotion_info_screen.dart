import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class AffiliatePromotionInfoScreen extends StatelessWidget {
  const AffiliatePromotionInfoScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        isCartVisible: false,
        isMembershipVisible: false,
        isDefaultMenuVisible: false,
        backgroundColor: AppColors.appWhite,
        onTapLeading: () => Navigator.pop(context),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Sell More.Effortlessly.",
                style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 16),
              Text(
                "Swadesic's Influencer Promotion Program lets creators promote your products, and you only pay when they bring you a customer.",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 24),
              ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.asset(
                  'assets/media/affiliate_promotion_image.jpg',
                  width: double.infinity,
                ),
              ),
              const SizedBox(height: 24),

              Text(
                "How it works",
                style: AppTextStyle.access1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 16),
              _buildStep(
                "1",
                "Set a commission",
                "Choose how much to reward per sale.",
              ),
              _buildStep(
                "2",
                "Influencers promote",
                "They create content & drive traffic",
              ),
              _buildStep(
                "3",
                "You get sales",
                "More orders, zero extra work",
              ),
              _buildStep(
                "4",
                "They get paid",
                "Swadesic takes a small fee, influencer gets their cut",
              ),
              const SizedBox(height: 24),
              Text(
                "Why Join?",
                style: AppTextStyle.access1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 16),
              _buildBenefit(
                "Zero risk",
                "No upfront spending. Pay only when a sale happens.",
              ),
              _buildBenefit(
                "Hands-off marketing",
                "Influencers do all the work. You just sell.",
              ),
              _buildBenefit(
                "Massive reach",
                "Get your products in front of the right audience, instantly.",
              ),
              const SizedBox(height: 24),
              Text(
                " More sales. More reach. Set your commission and let the system work for you.",
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(11),
                    ),
                  ),
                  child: Text(
                    AppStrings.gotIt,
                    style: AppTextStyle.access0(
                      textColor: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStep(String number, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$number. ",
             style: AppTextStyle.access0(textColor: AppColors.appBlack)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                   style: AppTextStyle.access0(textColor: AppColors.appBlack)),
                Text(
                  description,
                   style: AppTextStyle.contentText0(textColor: AppColors.appBlack)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefit(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: const Icon(
              Icons.check,
              color: Colors.green,
              size: 20,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyle.access0(textColor: AppColors.appBlack)),
                Text(
                  description,
                  style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
