/// Stub implementation for non-web platforms
/// This file provides empty implementations for platforms that don't support web functionality
class WebUrlManager {
  /// Updates the browser URL with the given path and optional title
  static void updateUrl(String path, [String? title]) {
    // No-op for non-web platforms
  }

  /// Gets the current URL
  static String getCurrentUrl() {
    return '';
  }

  /// Checks if the current platform supports URL management
  static bool get isSupported => false;

  /// Initializes URL capture for web app
  static void initializeUrlCapture() {
    // No-op for non-web platforms
  }

  /// Gets referral code from current URL
  static String? getReferralCodeFromUrl() {
    return null;
  }

  /// Sets up URL listener for navigation changes
  static void setupUrlListener(Function? onUrlChange) {
    // No-op for non-web platforms
  }
}
