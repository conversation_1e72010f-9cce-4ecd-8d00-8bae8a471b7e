import 'package:flutter_test/flutter_test.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';

void main() {
  group('Updated Shopping Cart Variant Tests', () {
    test('should create CartProduct with new variant structure', () {
      final cartProduct = CartProduct(
        productid: 112,
        productReference: 'P1748335610278489ULRX',
        productName: 'Digene',
        brandName: 'KittenKreeps',
        mrpPrice: 250,
        sellingPrice: 20,
        variantReference: 'PV1751286532031994',
        variantMrpPrice: 296,
        variantSellingPrice: 50,
        variantStockQuantity: 360,
        options: {
          'Style': ['Cyberpunk', 'Classic'],
          'Color': ['Green', 'Orange']
        },
        productVariants: [
          ProductVariantData(
            productVariantId: 134,
            variantReference: 'PV1751286346265596',
            productReferenceId: 'P1748335610278489ULRX',
            combinations: {'Color': 'Green'},
            mrpPrice: 200,
            sellingPrice: 199,
            stock: 256,
          ),
          ProductVariantData(
            productVariantId: 135,
            variantReference: 'PV1751286532031994',
            productReferenceId: 'P1748335610278489ULRX',
            combinations: {'Color': 'Orange', 'Style': 'Cyberpunk'},
            mrpPrice: 296,
            sellingPrice: 50,
            stock: 360,
          ),
        ],
      );

      expect(cartProduct.variantReference, 'PV1751286532031994');
      expect(cartProduct.variantMrpPrice, 296);
      expect(cartProduct.variantSellingPrice, 50);
      expect(cartProduct.variantStockQuantity, 360);
      expect(cartProduct.options!['Style']!.length, 2);
      expect(cartProduct.options!['Color']!.length, 2);
      expect(cartProduct.productVariants!.length, 2);
    });

    test('should serialize and deserialize CartProduct with new variant structure', () {
      final originalProduct = CartProduct(
        productid: 112,
        productReference: 'P1748335610278489ULRX',
        productName: 'Digene',
        brandName: 'KittenKreeps',
        mrpPrice: 250,
        sellingPrice: 20,
        variantReference: 'PV1751286532031994',
        variantMrpPrice: 296,
        variantSellingPrice: 50,
        variantStockQuantity: 360,
        options: {
          'Style': ['Cyberpunk', 'Classic'],
          'Color': ['Green', 'Orange']
        },
        productVariants: [
          ProductVariantData(
            productVariantId: 135,
            variantReference: 'PV1751286532031994',
            productReferenceId: 'P1748335610278489ULRX',
            combinations: {'Color': 'Orange', 'Style': 'Cyberpunk'},
            mrpPrice: 296,
            sellingPrice: 50,
            stock: 360,
          ),
        ],
      );

      // Convert to JSON
      final json = originalProduct.toJson();
      
      // Convert back from JSON
      final deserializedProduct = CartProduct.fromJson(json);

      expect(deserializedProduct.variantReference, originalProduct.variantReference);
      expect(deserializedProduct.variantMrpPrice, originalProduct.variantMrpPrice);
      expect(deserializedProduct.variantSellingPrice, originalProduct.variantSellingPrice);
      expect(deserializedProduct.variantStockQuantity, originalProduct.variantStockQuantity);
      expect(deserializedProduct.options!['Style']!.length, originalProduct.options!['Style']!.length);
      expect(deserializedProduct.productVariants!.length, originalProduct.productVariants!.length);
    });

    test('should parse API response correctly', () {
      final apiResponse = {
        "productid": 112,
        "product_reference": "P1748335610278489ULRX",
        "product_name": "Digene",
        "brand_name": "KittenKreeps",
        "mrp_price": 250,
        "selling_price": 20,
        "variant_mrp_price": 296,
        "variant_selling_price": 50,
        "variant_stock_quantity": 360,
        "variant_reference": "PV1751286532031994",
        "options": {
          "Style": ["Cyberpunk", "Classic"],
          "Color": ["Green", "Orange"]
        },
        "product_variants": [
          {
            "product_variantid": 134,
            "variant_reference": "PV1751286346265596",
            "product_reference_id": "P1748335610278489ULRX",
            "combinations": {"Color": "Green"},
            "mrp_price": 200,
            "selling_price": 199,
            "stock": 256,
            "is_active": true
          },
          {
            "product_variantid": 135,
            "variant_reference": "PV1751286532031994",
            "product_reference_id": "P1748335610278489ULRX",
            "combinations": {"Color": "Orange", "Style": "Cyberpunk"},
            "mrp_price": 296,
            "selling_price": 50,
            "stock": 360,
            "is_active": true
          }
        ]
      };

      final cartProduct = CartProduct.fromJson(apiResponse);

      expect(cartProduct.productid, 112);
      expect(cartProduct.productReference, "P1748335610278489ULRX");
      expect(cartProduct.variantReference, "PV1751286532031994");
      expect(cartProduct.variantMrpPrice, 296);
      expect(cartProduct.variantSellingPrice, 50);
      expect(cartProduct.variantStockQuantity, 360);
      expect(cartProduct.options!["Style"]!.contains("Cyberpunk"), true);
      expect(cartProduct.options!["Color"]!.contains("Orange"), true);
      expect(cartProduct.productVariants!.length, 2);
      
      // Test specific variant
      final selectedVariant = cartProduct.productVariants!.firstWhere(
        (v) => v.variantReference == "PV1751286532031994"
      );
      expect(selectedVariant.combinations["Color"], "Orange");
      expect(selectedVariant.combinations["Style"], "Cyberpunk");
      expect(selectedVariant.mrpPrice, 296);
      expect(selectedVariant.sellingPrice, 50);
      expect(selectedVariant.stock, 360);
    });

    test('should handle products without variants', () {
      final cartProduct = CartProduct(
        productid: 1,
        productReference: 'PROD123',
        productName: 'Simple Product',
        brandName: 'Test Brand',
        mrpPrice: 1000,
        sellingPrice: 800,
        // No variant information
      );

      expect(cartProduct.variantReference, null);
      expect(cartProduct.variantMrpPrice, null);
      expect(cartProduct.variantSellingPrice, null);
      expect(cartProduct.options, null);
      expect(cartProduct.productVariants, null);
    });

    test('should correctly identify products with variants', () {
      // Product with variants
      final productWithVariants = CartProduct(
        productid: 1,
        productReference: 'PROD123',
        productName: 'Test Product',
        brandName: 'Test Brand',
        mrpPrice: 2000,
        sellingPrice: 1500,
        variantReference: 'VAR123',
        options: {'Size': ['S', 'M', 'L']},
        productVariants: [
          ProductVariantData(
            combinations: {'Size': 'M'},
            mrpPrice: 2000,
            sellingPrice: 1500,
            stock: 10,
          ),
        ],
      );

      // Product without variants
      final productWithoutVariants = CartProduct(
        productid: 2,
        productReference: 'PROD456',
        productName: 'Simple Product',
        brandName: 'Test Brand',
        mrpPrice: 1000,
        sellingPrice: 800,
      );

      // Test the logic for variant detection
      bool hasVariants1 = productWithVariants.options != null && 
                         productWithVariants.options!.isNotEmpty &&
                         productWithVariants.productVariants != null &&
                         productWithVariants.productVariants!.isNotEmpty;
      
      bool hasVariants2 = productWithoutVariants.options != null && 
                         productWithoutVariants.options!.isNotEmpty &&
                         productWithoutVariants.productVariants != null &&
                         productWithoutVariants.productVariants!.isNotEmpty;

      expect(hasVariants1, true);
      expect(hasVariants2, false);
    });

    test('should generate correct variant display text from new structure', () {
      final cartProduct = CartProduct(
        productid: 1,
        productReference: 'PROD123',
        productName: 'Test Product',
        brandName: 'Test Brand',
        mrpPrice: 2000,
        sellingPrice: 1500,
        variantReference: 'VAR123',
        productVariants: [
          ProductVariantData(
            variantReference: 'VAR123',
            combinations: {'Size': 'M', 'Color': 'Red'},
            mrpPrice: 2000,
            sellingPrice: 1500,
            stock: 10,
          ),
        ],
      );

      // Test the display text generation logic
      final selectedVariant = cartProduct.productVariants!.firstWhere(
        (variant) => variant.variantReference == cartProduct.variantReference,
        orElse: () => cartProduct.productVariants!.first,
      );
      
      List<String> displayParts = [];
      if (selectedVariant.combinations.isNotEmpty) {
        selectedVariant.combinations.forEach((key, value) {
          displayParts.add("$key: $value");
        });
      }
      String displayText = displayParts.join(", ");

      expect(displayText, contains("Size: M"));
      expect(displayText, contains("Color: Red"));
      expect(displayText, contains(", "));
    });

    test('should use variant pricing when available', () {
      final cartProduct = CartProduct(
        productid: 1,
        productReference: 'PROD123',
        productName: 'Test Product',
        brandName: 'Test Brand',
        mrpPrice: 2000,
        sellingPrice: 1500,
        variantMrpPrice: 2500,
        variantSellingPrice: 1800,
      );

      // Test pricing logic
      int displaySellingPrice = cartProduct.variantSellingPrice ?? cartProduct.sellingPrice ?? 0;
      int displayMrpPrice = cartProduct.variantMrpPrice ?? cartProduct.mrpPrice ?? 0;

      expect(displaySellingPrice, 1800); // Should use variant price
      expect(displayMrpPrice, 2500); // Should use variant price
    });

    test('should fallback to product pricing when variant pricing not available', () {
      final cartProduct = CartProduct(
        productid: 1,
        productReference: 'PROD123',
        productName: 'Test Product',
        brandName: 'Test Brand',
        mrpPrice: 2000,
        sellingPrice: 1500,
        // No variant pricing
      );

      // Test pricing logic
      int displaySellingPrice = cartProduct.variantSellingPrice ?? cartProduct.sellingPrice ?? 0;
      int displayMrpPrice = cartProduct.variantMrpPrice ?? cartProduct.mrpPrice ?? 0;

      expect(displaySellingPrice, 1500); // Should use product price
      expect(displayMrpPrice, 2000); // Should use product price
    });
  });
}
