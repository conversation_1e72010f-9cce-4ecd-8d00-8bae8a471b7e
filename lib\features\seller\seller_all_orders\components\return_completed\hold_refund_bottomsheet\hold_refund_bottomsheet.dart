import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_completed/return_completed_bloc.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';

class HoldRefundBottomSheet extends StatefulWidget {
  final ReturnCompletedBloc returnCompletedBloc;
  final SubOrder subOrder;
  const HoldRefundBottomSheet({Key? key, required this.returnCompletedBloc, required this.subOrder}) : super(key: key);

  @override
  State<HoldRefundBottomSheet> createState() => _HoldRefundBottomSheetState();
}

class _HoldRefundBottomSheetState extends State<HoldRefundBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          AppTitleAndOptions(
            title: AppStrings.pleaseExplainWhyYouWantsToHoldRefund,
            option: AppTextFields.allTextField(
              context: context,
              minLines: 5,
              maxEntry: 200,
              maxLines: 200,
              textEditingController: widget.returnCompletedBloc.holdingRefundReasonTextCtrl,
              hintText: AppStrings.note,
            ),
          ),
          verticalSizedBox(10),
          Row(
            children: [
              Expanded(
                child: AppCommonWidgets.activeButton(buttonName: "Yes", onTap: (){
                  widget.returnCompletedBloc.holdRefundApi(subOrder: widget.subOrder);
                }),
              ),
              horizontalSizedBox(20),
              Expanded(
                child: AppCommonWidgets.inActiveButton(buttonName: "No", onTap: (){
                  Navigator.pop(context);
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }
  //endregion
}
