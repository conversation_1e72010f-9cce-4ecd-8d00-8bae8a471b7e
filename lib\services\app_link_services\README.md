# Page URL Service - Universal Web App URL Management

This service provides a modular solution for managing web app URLs without requiring manual commenting/uncommenting based on the app type.

## Problem Solved

Previously, you had to manually comment/uncomment lines like this in every page:
```dart
// uncomment this for web app
// if (kIsWeb) {
//   WidgetsBinding.instance.addPostFrameCallback((_) {
//     html.window.history.pushState(null, 'Help', '/help');
//   });
// }
```

## Solution

The `PageUrlService` automatically handles platform detection and only executes web-specific code when running on web platform.

## Usage

### Basic Usage

```dart
import 'package:swadesic/services/app_link_services/page_url_service.dart';

class MyScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // This automatically works on web and does nothing on mobile
    PageUrlService.setPageUrlAfterBuild('/my-page', 'My Page Title');
    
    return Scaffold(
      // Your widget content
    );
  }
}
```

### Available Methods

1. **setPageUrl(path, title)** - Sets URL immediately
2. **setPageUrlAfterBuild(path, title)** - Sets URL after widget build (recommended for build methods)
3. **getCurrentUrl()** - Gets current URL (web only)
4. **initialize()** - Initialize URL capture (call once during app startup)
5. **isSupported** - Check if platform supports URL management
6. **setupUrlListener(callback)** - Listen for URL changes
7. **getReferralCodeFromUrl()** - Extract referral code from URL

### Examples

#### In a StatelessWidget
```dart
@override
Widget build(BuildContext context) {
  PageUrlService.setPageUrlAfterBuild('/help', 'Help');
  return Scaffold(...);
}
```

#### In a StatefulWidget
```dart
@override
void initState() {
  super.initState();
  PageUrlService.setPageUrl('/profile', 'User Profile');
}
```

#### For Payment Pages
```dart
@override
Widget build(BuildContext context) {
  PageUrlService.setPageUrlAfterBuild('/payment', 'Payment');
  return PaymentWidget(...);
}
```

## Benefits

1. **No Manual Platform Checks** - Service handles `kIsWeb` checks internally
2. **No Commenting/Uncommenting** - Works automatically on all platforms
3. **Safe for Mobile** - Does nothing on mobile platforms, no crashes
4. **Consistent API** - Same method calls work everywhere
5. **Error Handling** - Built-in error handling prevents crashes
6. **Modular** - Can be used in any page without setup

## Migration Guide

### Before (Manual)
```dart
// uncomment this for web app
// if (kIsWeb) {
//   WidgetsBinding.instance.addPostFrameCallback((_) {
//     html.window.history.pushState(null, 'Help', '/help');
//   });
// }
```

### After (Automatic)
```dart
PageUrlService.setPageUrlAfterBuild('/help', 'Help');
```

## Files Updated

- `lib/services/app_link_services/page_url_service.dart` - Main service
- `lib/services/app_link_services/web_url_manager_web.dart` - Web implementation
- `lib/services/app_link_services/web_url_manager_stub.dart` - Mobile stub
- `lib/features/help/help_screen.dart` - Example usage
- `lib/services/app_link_services/web_app_capture_url.dart` - Updated to use service

## Technical Details

The service uses conditional imports to automatically load the correct implementation:
- Web: Uses `dart:html` for actual URL management
- Mobile: Uses stub implementation that does nothing

This ensures the code compiles and runs correctly on all platforms without manual intervention.
