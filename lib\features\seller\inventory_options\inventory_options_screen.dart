import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/inventory_options/inventory_options_bloc.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_text_style.dart';

class InventoryOptionsScreen extends StatefulWidget {
  final String storeReference;
  final Product product;

  const InventoryOptionsScreen({
    super.key,
    required this.storeReference,
    required this.product,
  });

  @override
  State<InventoryOptionsScreen> createState() => _InventoryOptionsScreenState();
}

class _InventoryOptionsScreenState extends State<InventoryOptionsScreen> {
  late InventoryOptionsBloc inventoryOptionsBloc;

  @override
  void initState() {
    inventoryOptionsBloc = InventoryOptionsBloc(
      context,
      widget.storeReference,
      widget.product,
    );
    inventoryOptionsBloc.init();
    super.initState();
  }

  @override
  void dispose() {
    inventoryOptionsBloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(child: body()),
    );
  }

  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.inventoryOptions,
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: true,
      textButtonWidget: AppCommonWidgets.appBarTextButtonText(text: AppStrings.save),
      onTapTextButton: () {
        inventoryOptionsBloc.onTapSave();
      },
    );
  }

  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        inventoryOptionsBloc.init();
      },
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        children: [
          verticalSizedBox(20),
          hasOptionsToggle(),
          verticalSizedBox(30),
          StreamBuilder<bool>(
            stream: inventoryOptionsBloc.refreshCtrl.stream,
            builder: (context, snapshot) {
              return Column(
                children: [
                  if (inventoryOptionsBloc.hasOptions) ...[
                    productOptionsSection(),
                    verticalSizedBox(30),
                    productVariantsSection(),
                  ],
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget hasOptionsToggle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.doesThisProductHaveOptions,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(15),
        StreamBuilder<bool>(
          stream: inventoryOptionsBloc.refreshCtrl.stream,
          builder: (context, snapshot) {
            return Row(
              children: [
                GestureDetector(
                  onTap: () => inventoryOptionsBloc.setHasOptions(true),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.appBlack),
                          color: inventoryOptionsBloc.hasOptions 
                              ? AppColors.appBlack 
                              : AppColors.appWhite,
                        ),
                        child: inventoryOptionsBloc.hasOptions
                            ? const Icon(Icons.check, color: AppColors.appWhite, size: 16)
                            : null,
                      ),
                      horizontalSizedBox(8),
                      Text(
                        AppStrings.yes,
                        style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                      ),
                    ],
                  ),
                ),
                horizontalSizedBox(20),
                GestureDetector(
                  onTap: () => inventoryOptionsBloc.setHasOptions(false),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.appBlack),
                          color: !inventoryOptionsBloc.hasOptions 
                              ? AppColors.appBlack 
                              : AppColors.appWhite,
                        ),
                        child: !inventoryOptionsBloc.hasOptions
                            ? const Icon(Icons.check, color: AppColors.appWhite, size: 16)
                            : null,
                      ),
                      horizontalSizedBox(8),
                      Text(
                        AppStrings.no,
                        style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget productOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppStrings.productOptions,
              style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
            ),
            GestureDetector(
              onTap: () => inventoryOptionsBloc.showAddOptionBottomSheet(),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.appBlack,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.add, color: AppColors.appWhite, size: 16),
                    horizontalSizedBox(4),
                    Text(
                      AppStrings.add,
                      style: AppTextStyle.contentText0(textColor: AppColors.appWhite),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        verticalSizedBox(15),
        StreamBuilder<bool>(
          stream: inventoryOptionsBloc.refreshCtrl.stream,
          builder: (context, snapshot) {
            if (inventoryOptionsBloc.productOptions.isEmpty) {
              return Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    AppStrings.noOptionsAddedYet,
                    style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  ),
                ),
              );
            }
            
            return Column(
              children: inventoryOptionsBloc.productOptions.map((option) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: GestureDetector(
                    onTap: () => inventoryOptionsBloc.showEditOptionBottomSheet(option),
                    child: Row(
                      children: [
                        Expanded(
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: "${option.optionName}: ",
                                  style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                                      .copyWith(fontWeight: FontWeight.w600),
                                ),
                                TextSpan(
                                  text: option.optionValues.join(', '),
                                  style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Icon(Icons.arrow_forward_ios, size: 16, color: AppColors.appBlack),
                      ],
                    ),
                  ),
                );
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget productVariantsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppStrings.productVariants,
              style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
            ),
            GestureDetector(
              onTap: () => inventoryOptionsBloc.showAddVariantBottomSheet(),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.appBlack,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.add, color: AppColors.appWhite, size: 16),
                    horizontalSizedBox(4),
                    Text(
                      AppStrings.add,
                      style: AppTextStyle.contentText0(textColor: AppColors.appWhite),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        verticalSizedBox(15),
        StreamBuilder<bool>(
          stream: inventoryOptionsBloc.refreshCtrl.stream,
          builder: (context, snapshot) {
            if (inventoryOptionsBloc.productVariants.isEmpty) {
              return Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    AppStrings.noVariantsAddedYet,
                    style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  ),
                ),
              );
            }
            
            return Column(
              children: inventoryOptionsBloc.productVariants.map((variant) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: GestureDetector(
                    onTap: () => inventoryOptionsBloc.showEditVariantBottomSheet(variant),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Variant combination display
                              Row(
                                children: variant.combinations.entries.map((entry) {
                                  return Container(
                                    margin: const EdgeInsets.only(right: 8, bottom: 8),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          entry.key,
                                          style: AppTextStyle.access0(textColor: AppColors.appBlack)
                                              .copyWith(fontWeight: FontWeight.w600),
                                        ),
                                        Text(
                                          entry.value,
                                          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                              ),
                              verticalSizedBox(8),
                              // Pricing and stock info
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Selling price",
                                          style: AppTextStyle.access0(textColor: AppColors.appBlack),
                                        ),
                                        Row(
                                          children: [
                                            Text(
                                              "₹${variant.sellingPrice}",
                                              style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                                                  .copyWith(fontWeight: FontWeight.w600),
                                            ),
                                            if (variant.mrpPrice > variant.sellingPrice) ...[
                                              horizontalSizedBox(8),
                                              Text(
                                                "₹${variant.mrpPrice}",
                                                style: AppTextStyle.access0(textColor: AppColors.appBlack)
                                                    .copyWith(
                                                  decoration: TextDecoration.lineThrough,
                                                ),
                                              ),
                                              horizontalSizedBox(4),
                                              Text(
                                                "${variant.discountPercentage.round()}% OFF",
                                                style: AppTextStyle.access0(textColor: Colors.green)
                                                    .copyWith(fontWeight: FontWeight.w600),
                                              ),
                                            ],
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              verticalSizedBox(8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Stock",
                                          style: AppTextStyle.access0(textColor: AppColors.appBlack),
                                        ),
                                        Text(
                                          "${variant.stock}",
                                          style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                                              .copyWith(fontWeight: FontWeight.w600),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Icon(Icons.close, size: 20, color: AppColors.appBlack),
                      ],
                    ),
                  ),
                );
              }).toList(),
            );
          },
        ),
      ],
    );
  }
}
