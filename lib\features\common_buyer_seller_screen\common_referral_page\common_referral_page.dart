import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_how_swadesic.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_your_referral_makes.dart';
import 'package:swadesic/model/invite_reward_info/invite_reward_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class CommonReferralPage extends StatefulWidget {
  const CommonReferralPage({super.key});

  @override
  State<CommonReferralPage> createState() => _CommonReferralPageState();
}

class _CommonReferralPageState extends State<CommonReferralPage> {

  //region Bloc
  late CommonReferralPageBloc commonReferralPageBloc;
  //endregion

  //region Init
  @override
  void initState() {
    commonReferralPageBloc = CommonReferralPageBloc(context);
    commonReferralPageBloc.init();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: Container(
        color: AppColors.appWhite,
        child: body(),
      ),
    );
  }


  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title:"App referral" ,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion
  Widget challengeSection() {

    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.symmetric(horizontal: 0),
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
      decoration: BoxDecoration(
        color: AppColors.appBlack,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                "⚡",
                style: TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 5),
              Text(
                "The Challenge:",
                style: AppTextStyle.introSlideTitle(textColor: Colors.white)
                    .copyWith(fontSize: 22, fontFamily: AppConstants.leagueSemiBold),
              ),
            ],
          ),
          Text(
            "Bring 3 Buyers. Bring 1 Seller,\nToday. Change the Game.",
            style: AppTextStyle.introSlideTitle(textColor: Colors.white)
                .copyWith(fontSize: 22, fontFamily: AppConstants.leagueSemiBold),
          ),
        ],
      ),
    );
  }




  //region Body
  Widget body(){
    return StreamBuilder<CommonReferralPageState>(
      stream: commonReferralPageBloc.commonReferralPageStateCtrl.stream,
      initialData:CommonReferralPageState.Loading ,
      builder: (context, snapshot) {
        //Success
        if(snapshot.data == CommonReferralPageState.Success){
          return SingleChildScrollView(
            child: Column(
              children: [
                indiaFlag(),
                join(),
                yourReferralMakesADifference(),
                // const SizedBox(height: 30),
                // challengeSection(),
                // const SizedBox(height: 30),
                // rewardSection(),
                // const SizedBox(height: 30),
                // howSwadesic(),
                const SizedBox(height: 30),
                footer(),

              ],
            ),
          );
        }
        //Loading
        if(snapshot.data == CommonReferralPageState.Loading){
          return AppCommonWidgets.appCircularProgress();
        }
        return const SizedBox();
      }
    );
  }
  //endregion


//region Inda flag
Widget indiaFlag(){
    return Stack(
      children: [
        Container(
          width: 375,
          height: 235,
          margin: const EdgeInsets.only(left: 0, right: 0, top: 60, bottom: 0),
          child: Image.asset(AppImages.missionPageImage,fit: BoxFit.fill),
        ),
        Positioned.fill(
            child: Container(
              margin: const EdgeInsets.only(bottom: 5),
                alignment: Alignment.bottomCenter,
                // child: Text("Build Atmanirbhar Bharat helping  Swadeshi businesses!",style: AppTextStyle.introSlideTitle(textColor: AppColors.appWhite).copyWith(fontSize: 13,fontFamily: AppConstants.leagueSemiBold),)))
                )
        )
      ],
    );
}
//endregion


  //region Join the swadesic
  Widget bulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15, right: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10, right: 10, left: 10),
            child: Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: AppColors.appBlack,
                shape: BoxShape.circle,
              ),
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
            ),
          ),
        ],
      ),
    );
  }

  Widget join(){
    return Container(
      margin: EdgeInsets.only(top: 20,bottom: 10,right: 15,left: 15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Help us accelerate Swadeshi Mission",
            style: AppTextStyle.exHeading2(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),

          Text(
            "India, that is Bharat, can only be prosperous if money stays within India. Our nation leaks ₹2.2 lakh crores in net imports and exports. That equals to 350 Chandrayan-3 missions.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),
          bulletPoint(
            "We can’t negotiate critical goods like Defense, Energy and Technology sectors."
          ),
          bulletPoint(
            "At least we can make a strong Swadeshi Economy in other sectors. Swadesic is an initiative to support Indian business ecosystem by being a technology partner & accelerator"
          ),
          bulletPoint(
            "For someone who runs a business, Swadesic provides a free store to run their operations. Unlike in other platforms, swadesic store is a digital asset that gets registered to user’s phone number."
          ),
          bulletPoint(
            "With Swadesic stores, businesses can build business & community at one place. Offer a professional shopping & an engaging brand experience to their customers. "
          ),
          bulletPoint(
            "For consumers, Swadesic opens up a safe place to shop directly from brands they love on social media and localities. Consumers can know more about the brands, people behind it and connect with community of like minded."
          ),
          const SizedBox(height:40)
        ],
      ),
    );
  }
  //endregion


//region Your referral makes a difference
Widget yourReferralMakesADifference(){
    return  CommonReferralYourReferralMakes(commonReferralPageBloc: commonReferralPageBloc);
}
//endregion

//region How swadesic
Widget howSwadesic(){
    return  CommonReferralHowSwadesic(commonReferralPageBloc: commonReferralPageBloc);
}
//endregion

  Widget rewardSection() {
    return Container(
      margin: EdgeInsets.only(top: 20, bottom: 10, right: 20, left: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Because if 10 Crore Indians brought just one more, we'd flip the entire economy in months.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
          SizedBox(height: 20),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  "💰 Your Reward? A stronger Bharat. A self-reliant economy. Oh, and exclusive perks just for being part of the revolution.",
                  style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

//region Footer
Widget footer(){
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Be part of India's growth story with Swadesic. This isn't just a referral. This is a revolution.",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 30),
          Text(
            '"Bharat is calling for her noblest sons. Will you answer the call?"',
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 10),
          Text(
            "— Inspired by Netaji Subhas Chandra Bose",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
          ),
        ],
      ),
    );
  }
//endregion




}
