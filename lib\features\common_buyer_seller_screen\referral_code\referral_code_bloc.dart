import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/common_buyer_seller_screen/paste_cancel/paste_cancel.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class ReferralCodeBloc{
  //region Common variable
  late BuildContext context;
  bool isHintHide = true;
  //endregion

  final GlobalKey<FormState> userFormKey = GlobalKey<FormState>();
//region Text Editing Controller
  static final firstFieldTextCtrl = TextEditingController();
  static final secondFieldTextCtrl = TextEditingController();
  static final thirdFieldTextCtrl = TextEditingController();
  TextInputType textInputType = TextInputType.text;
  FocusNode? firstFocusNode = FocusNode();
  FocusNode? secondFocusNode = FocusNode();
  FocusNode? thirdFocusNode = FocusNode();

//endregion

  //region Sty

//region Controller
  final refreshCtrl = StreamController<bool>.broadcast();

//endregion
  //region Constructor
  ReferralCodeBloc(this.context);
  //endregion

  //region Init
  void init(){


    secondFocusNode!.addListener(() {
      secondFocusListen();
    });

  }
  //endregion

//region First text field change
onChangeFirstTextFieldChange({required String value}){



  ///Convert text to capital
  firstFieldTextCtrl.text = value.toUpperCase();
  firstFieldTextCtrl.selection = TextSelection.fromPosition(TextPosition(offset: firstFieldTextCtrl.text.length));

  // firstFieldTextCtrl.selection = TextSelection(
  //     baseOffset: firstFieldTextCtrl.text.length,
  //     extentOffset: firstFieldTextCtrl.text.length);

  ///Jump to next field if length is equal to max
  if(value.length==1){
    FocusScope.of(context).requestFocus(secondFocusNode);
  }
  refreshCtrl.sink.add(true);

}
//endregion


//region Second text field change
  onChangeSecondTextFieldChange({required String value}){
    ///Convert text to capital
    secondFieldTextCtrl.text = value.toUpperCase();
    ///Move cursor to the all the way right
    secondFieldTextCtrl.selection = TextSelection.fromPosition(TextPosition(offset: secondFieldTextCtrl.text.length));
    ///If field is not empty then focus previous one
    if(secondFieldTextCtrl.text.isEmpty){
      return FocusScope.of(context).requestFocus(firstFocusNode);
    }
    ///Jump to next field if length is equal to max
    if(value.length==4){
      return FocusScope.of(context).requestFocus(thirdFocusNode);
    }
    refreshCtrl.sink.add(true);

  }
//endregion

//region Third text field change
  onChangeThirdTextFieldChange({required String value}){
    thirdFieldTextCtrl.selection = TextSelection.fromPosition(TextPosition(offset: thirdFieldTextCtrl.text.length));

    ///If field is not empty then focus previous one
    // if(thirdFieldTextCtrl.text.isEmpty){
    //   FocusScope.of(context).requestFocus(secondFocusNode);
    // }


    refreshCtrl.sink.add(true);

  }
//endregion


  //region On second focus listen
  void secondFocusListen(){

    // if (secondFocusNode!.hasFocus && ReferralCodeBloc.secondFieldTextCtrl.text.isEmpty) {
    //   FocusScope.of(context).requestFocus(firstFocusNode);
    // }
  }
  //endregion


  //region Paste dialog
  Future pasteDialog(){
    return CommonMethods.appDialogBox(
        context: context,
        widget: PasteCancel(onTapPaste:onPaste,)
    );
  }
//endregion


  //region On paste
  void onPaste()async{
    //Clear all text fields
    firstFieldTextCtrl.clear();
    secondFieldTextCtrl.clear();
    thirdFieldTextCtrl.clear();

    String data = await CommonMethods.pasteText();

     data = data.toUpperCase();

    List<String> dataList = data.split("");







    //Fill first text
    if(dataList.isNotEmpty){
      ReferralCodeBloc.firstFieldTextCtrl.text = dataList[0];
      dataList.removeAt(0);
    }
    //Fill second field
    if(dataList.length >= 4){
      for(int i = 0; i < 4; i++){
        ReferralCodeBloc.secondFieldTextCtrl.text = ReferralCodeBloc.secondFieldTextCtrl.text + dataList[0];
        dataList.removeAt(0);
      }
    }
    else{
      ReferralCodeBloc.secondFieldTextCtrl.text = dataList.join();
      dataList.clear();
    }
    ///Fill third filed

    //Check if rest of the data contains integer or not. If not then return
    if(!dataList.join().contains(RegExp(AppConstants.onlyInt))){
      //Focus to last
      FocusScope.of(context).requestFocus(thirdFocusNode);

      //Close keyboard
      CommonMethods.closeKeyboard(context);
      return;
    }

    //Take out all int from list
   dataList = dataList.where((element) =>
   element.contains(RegExp(AppConstants.onlyInt))
   ).toList();

    //If list is equal or greater then run this
    if(dataList.length >= 4){
      for(int i = 0; i < 4; i++){
        ReferralCodeBloc.thirdFieldTextCtrl.text = ReferralCodeBloc.thirdFieldTextCtrl.text + dataList[0];
        dataList.removeAt(0);
      }
    }
    else{
      ReferralCodeBloc.thirdFieldTextCtrl.text = dataList.join();
      dataList.clear();
    }
    //Close keyboard
    CommonMethods.closeKeyboard(context);

    // for(int i = dataList.length-1;  i >=0; i-- ){
    //   //print(dataList[i]);
    // }



  }
  //endregion


//region Dispose
void dispose(){
  firstFieldTextCtrl.clear();
  secondFieldTextCtrl.clear();
  thirdFieldTextCtrl.clear();
}
//endregion


}