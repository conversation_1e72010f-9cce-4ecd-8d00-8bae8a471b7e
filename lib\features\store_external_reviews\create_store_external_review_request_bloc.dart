import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/store_external_reviews/store_external_review_link_created_screen.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/services/store_external_review_service/store_external_review_service.dart';
import 'package:swadesic/util/common_methods.dart';

enum StoreExternalReviewRequestState { initial, loading, success, failed }

class CreateStoreExternalReviewRequestBloc {
  // region Common Variables
  BuildContext context;
  StoreInfo store;
  String? storeImage;

  // User identifier (username, phone, or email)
  TextEditingController userIdentifierController = TextEditingController();

  // Selected identifier type
  String selectedIdentifierType = 'username'; // Default to username

  // Store external review service
  final StoreExternalReviewService _storeExternalReviewService = StoreExternalReviewService();

  // State controller
  final stateCtrl = StreamController<StoreExternalReviewRequestState>.broadcast();

  // Response data
  Map<String, dynamic>? responseData;

  // endregion

  // region | Constructor |
  CreateStoreExternalReviewRequestBloc(
      this.context, this.store, this.storeImage);
  // endregion

  // region Init
  void init() {
    // Initialize any required data
  }
  // endregion

  // region On tap create link
  Future<void> onTapCreateLink() async {
    try {
      // Validate input
      if (userIdentifierController.text.trim().isEmpty) {
        CommonMethods.toastMessage("Please enter a valid identifier", context);
        return;
      }

      // Set loading state
      stateCtrl.sink.add(StoreExternalReviewRequestState.loading);

      // Format user identifier based on selected type
      String userIdentifier = userIdentifierController.text.trim();

      // Check if a review already exists for this user and store
      bool reviewExists =
          await _storeExternalReviewService.checkStoreExternalReviewExists(
        storeReference: store.storeReference!,
        userIdentifier: userIdentifier,
      );

      if (reviewExists) {
        // Set failed state
        stateCtrl.sink.add(StoreExternalReviewRequestState.failed);
        if (context.mounted) {
          CommonMethods.toastMessage(
              "This user has already submitted a review for this store. Each user can only submit one review per store.",
              context);
        }
        return;
      }

      // Create store external review request
      responseData = await _storeExternalReviewService.createStoreExternalReviewRequest(
        storeReference: store.storeReference!,
        userIdentifier: userIdentifier,
      );

      // Set success state
      stateCtrl.sink.add(StoreExternalReviewRequestState.success);

      // Navigate to link created screen
      _navigateToLinkCreatedScreen();
    } catch (error) {
      // Set failed state
      stateCtrl.sink.add(StoreExternalReviewRequestState.failed);
      if (context.mounted) {
        CommonMethods.toastMessage(
            "Failed to create store review request. Please try again.", context);
      }
    }
  }
  // endregion

  // region Navigate to link created screen
  void _navigateToLinkCreatedScreen() {
    if (responseData != null) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StoreExternalReviewLinkCreatedScreen(
            token: responseData!['token'],
            expiresAt: responseData!['expires_at'],
            userIdentifier: responseData!['user_identifier'],
            storeReference: store.storeReference!,
            storeName: store.storeName!,
            storeImage: storeImage,
            storeHandle: store.storehandle!,
          );
        },
      ).then((_) {
        // When dialog is dismissed, pop the create store external review request screen
        // This will redirect user back to the store reviews screen
        Navigator.of(context).pop();
      });
    }
  }
  // endregion

  // region On tap know more
  void onTapKnowMore() {
    // Show information about store external reviews
    CommonMethods.appDialogBox(
      context: context,
      widget: AlertDialog(
        title: const Text('About Store External Reviews'),
        content: const Text(
            'Store external reviews allow customers who purchased from your store outside Swadesic to leave reviews. '
            'Create a review request link and share it with your customers to collect their feedback about your store.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  // endregion

  // region Dispose
  void dispose() {
    stateCtrl.close();
    userIdentifierController.dispose();
  }
  // endregion
}
