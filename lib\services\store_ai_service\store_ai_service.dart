import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreAiService {
  // region Common Variables
  late HttpService httpService;
  // endregion

  // region | Constructor |
  StoreAiService() {
    httpService = HttpService();
  }
  // endregion

  // region Get Feature Request Status
  Future<bool> getFeatureRequestStatus() async {
    try {
      String reference = AppConstants.appData.isStoreView! 
          ? AppConstants.appData.storeReference ?? ''
          : AppConstants.appData.userReference ?? '';
      String url = "${AppConstants.baseUrl}/app_common/get_feature_request/?reference=$reference";
      final response = await httpService.getApiCall(url);
      
      if (response['message'] == 'success') {
        return response['data']['requested_store_ai'] ?? false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  // endregion

  // region Add Feature Request
  Future<bool> addFeatureRequest() async {
    try {
      String url = "${AppConstants.baseUrl}/app_common/add_feature_request/";
      final body = {
        'reference': AppConstants.appData.isStoreView! 
            ? AppConstants.appData.storeReference ?? ''
            : AppConstants.appData.userReference ?? '',
        'store_ai': true,
        'product_affiliation': false,
      };

      final response = await httpService.postApiCall(body, url);
      return response['message'] == 'success';
    } catch (e) {
      return false;
    }
  }
  // endregion
}
