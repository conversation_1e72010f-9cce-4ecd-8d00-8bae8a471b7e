import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreCommentService {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  StoreCommentService() {
    httpService = HttpService();
  }

  // endregion

  // region Get Store Comments
  Future<List<PostDetail>> getStoreComments(
      {required String entityReference,
      required String visitorReference,
      int limit = 100,
      int offset = 0,
      List<String> commentTypes = const ['EXTERNAL_REVIEW', 'REVIEW']}) async {
    try {
      // Build comment types parameter
      String commentTypesParam = commentTypes.join('%2C');

      // Build URL
      String url = "${AppConstants.baseUrl}/lean/get_store_comments/"
          "?limit=$limit"
          "&offset=$offset"
          "&visitor_reference=$visitorReference"
          "&entity_reference=$entityReference"
          "&comment_types=$commentTypesParam"
          "&review_type=";

      // Execute Request
      Map<String, dynamic> response = await httpService.getApiCall(url);

      // Parse response
      if (response['message'] == 'success' && response['data'] != null) {
        List<PostDetail> comments = [];

        // Process actual API response data
        for (var commentData in response['data']) {
          // Convert comment data to PostDetail using the fromCommentJson method
          PostDetail comment = PostDetail.fromCommentJson(commentData);
          comments.add(comment);
        }

        // Add mock data for testing if no real data (using new API structure)
        if (comments.isEmpty) {
          // Mock review comment matching new API structure
          Map<String, dynamic> mockReviewData = {
            "comment_reference": "CO202506181815296192",
            "comment_text": "My comment for product 2",
            "created_date": "2025-06-18 18:15:29.518705+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": null,
            "level": 1,
            "comment_type": "REVIEW",
            "main_parent_id": "P1748859602906626RGWO",
            "commenter_reference": "U1744450273600",
            "user_reference": "U1744450273600",
            "store_reference": null,
            "tagged_references_json": [
              {"type": "USER", "order": 1, "reference": "U1749293572687"}
            ],
            "tagged_users_count": 1,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text":
                "verified review @hanuman_coderz/vegeta-poster",
            "content_headers": [
              {
                "handle": "@hanuman_coderz/vegeta-poster",
                "reference": "P1748859602906626RGWO"
              }
            ],
            "reviewed_reference_json": [
              {
                "icon": "post_images/1748859494851.jpg",
                "name": "Vegeta poster-Hanuman coders",
                "type": "PRODUCT",
                "order": 1,
                "handle": "@hanuman_coderz",
                "reference": "P1748859602906626RGWO"
              }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT",
            "icon": "/media/profile_image/er_1748858846831.jpg",
            "handle": "krishna_k",
            "name": "Krishna Kanth ",
            "reference": "U1744450273600"
          };

          PostDetail mockComment = PostDetail.fromCommentJson(mockReviewData);
          comments.add(mockComment);
        }

        return comments;
      } else {
        // Return mock data even if API fails for testing
        return _getMockComments();
      }
    } catch (e) {
      // Handle error - return mock data for testing
      return _getMockComments();
    }
  }

  // endregion

  // region Get Mock Comments for Testing
  List<PostDetail> _getMockComments() {
    List<PostDetail> comments = [];

    // Mock review comment (exact structure from requirements)
    Map<String, dynamic> mockReviewData = {
      "comment_reference": "CO202506181815296192",
      "comment_text": "My comment for product 2",
      "created_date": "2025-06-18 18:15:29.518705+05:30",
      "is_deleted": false,
      "like_count": 0,
      "comment_count": 0,
      "repost_count": 0,
      "repost_plus_count": 0,
      "save_count": 0,
      "share_count": 0,
      "analytics_view_count": 0,
      "rating_count": null,
      "level": 1,
      "comment_type": "REVIEW",
      "main_parent_id": "P1748859602906626RGWO",
      "commenter_reference": "U1744450273600",
      "user_reference": "U1744450273600",
      "store_reference": null,
      "tagged_references_json": [
        {"type": "USER", "order": 1, "reference": "U1749293572687"}
      ],
      "tagged_users_count": 1,
      "tagged_stores_count": 0,
      "tagged_products_count": 0,
      "comment_images": [],
      "content_header_text": "verified review @hanuman_coderz/vegeta-poster",
      "content_headers": [
        {
          "handle": "@hanuman_coderz/vegeta-poster",
          "reference": "P1748859602906626RGWO"
        }
      ],
      "reviewed_reference_json": [
        {
          "icon": "post_images/1748859494851.jpg",
          "name": "Vegeta poster-Hanuman coders",
          "type": "PRODUCT",
          "order": 1,
          "handle": "@hanuman_coderz",
          "reference": "P1748859602906626RGWO"
        }
      ],
      "save_status": false,
      "repost_status": false,
      "content_category": "POST",
      "like_status": false,
      "content_type": "COMMENT",
      "icon": "/media/profile_image/er_1748858846831.jpg",
      "handle": "krishna_k",
      "name": "Krishna Kanth ",
      "reference": "U1744450273600"
    };

    // Mock external review comment
    Map<String, dynamic> mockExternalReviewData = {
      "comment_reference": "CO202504141918241770",
      "comment_text": "My review local",
      "created_date": "2025-04-14 19:18:24.214682+05:30",
      "is_deleted": false,
      "like_count": 0,
      "comment_count": 0,
      "repost_count": 0,
      "repost_plus_count": 0,
      "save_count": 0,
      "share_count": 0,
      "analytics_view_count": 1,
      "rating_count": 4.0,
      "level": 1,
      "comment_type": "EXTERNAL_REVIEW",
      "main_parent_id": "P1748859602906626RGWO",
      "commenter_reference": "U1744450273600",
      "user_reference": "U1744450273600",
      "store_reference": null,
      "tagged_references_json": [
        {"type": "USER", "order": 1, "reference": "U1749293572687"}
      ],
      "tagged_users_count": 1,
      "tagged_stores_count": 0,
      "tagged_products_count": 0,
      "comment_images": [],
      "content_header_text": "external review @hanuman_coderz/vegeta-poster",
      "content_headers": [
        {
          "handle": "@hanuman_coderz/vegeta-poster",
          "reference": "P1748859602906626RGWO"
        }
      ],
      "reviewed_reference_json": [
        {
          "icon": "post_images/1748859494851.jpg",
          "name": "Vegeta poster-Hanuman coders",
          "type": "PRODUCT",
          "order": 1,
          "handle": "@hanuman_coderz",
          "reference": "P1748859602906626RGWO"
        }
      ],
      "save_status": false,
      "repost_status": false,
      "content_category": "POST",
      "like_status": false,
      "content_type": "COMMENT",
      "icon": "/media/profile_image/er_1748858846831.jpg",
      "handle": "krishna_k",
      "name": "Krishna Kanth ",
      "reference": "U1744450273600"
    };

    // Mock regular comment
    Map<String, dynamic> mockCommentData = {
      "comment_reference": "CO202506181815338817",
      "comment_text": "My comment for product 3",
      "created_date": "2025-06-18 18:15:33.885937+05:30",
      "is_deleted": false,
      "like_count": 0,
      "comment_count": 0,
      "repost_count": 0,
      "repost_plus_count": 0,
      "save_count": 0,
      "share_count": 0,
      "analytics_view_count": 0,
      "rating_count": null,
      "level": 1,
      "comment_type": "COMMENT",
      "main_parent_id": "P1748859602906626RGWO",
      "commenter_reference": "U1744450273600",
      "user_reference": "U1744450273600",
      "store_reference": null,
      "tagged_references_json": [
        {"type": "USER", "order": 1, "reference": "U1749293572687"}
      ],
      "tagged_users_count": 1,
      "tagged_stores_count": 0,
      "tagged_products_count": 0,
      "comment_images": [],
      "content_header_text": null,
      "content_headers": [],
      "reviewed_reference_json": [],
      "save_status": false,
      "repost_status": false,
      "content_category": "POST",
      "like_status": false,
      "content_type": "COMMENT",
      "icon": "/media/profile_image/er_1748858846831.jpg",
      "handle": "krishna_k",
      "name": "Krishna Kanth ",
      "reference": "U1744450273600"
    };

    // Add mock comments
    comments.add(PostDetail.fromCommentJson(mockReviewData));
    comments.add(PostDetail.fromCommentJson(mockExternalReviewData));
    comments.add(PostDetail.fromCommentJson(mockCommentData));

    return comments;
  }

  // endregion
}
