import 'package:flutter/cupertino.dart';

class GetCartDetailsResponse {
  String? message;
  List<CartStore>? cartStoreList;
  String deliveryNotes = "";
  bool isDeliveryNotesTextFieldVisible = false;

  GetCartDetailsResponse({this.message, this.cartStoreList});

  GetCartDetailsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    deliveryNotes = json['deliveryNotes']??"";
    if (json['data'] != null) {
      cartStoreList = <CartStore>[];
      json['data'].forEach((v) {
        cartStoreList!.add(CartStore.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['deliveryNotes'] = deliveryNotes;
    if (cartStoreList != null) {
      data['data'] = cartStoreList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CartStore {
  int? storeid;
  String? icon;
  String? storeReference;
  String? storehandle;
  String? storeName;
  String? storeDesc;
  TrustCenter? trustCenter;
  String? sellerNote = "";
  List<CartProduct>? cartProductList;
  bool? openForOrder;
  bool isDropDown = true;
  bool isSellerNoteTextFieldVisible = false;
  bool? isDeleted;

  CartStore(
      {this.storeid,
        this.icon,
        this.storeReference,
        this.storehandle,
        this.storeName,
        this.storeDesc,
        this.openForOrder,
        this.isDeleted,
        this.trustCenter,
        this.cartProductList});

  CartStore.fromJson(Map<String, dynamic> json) {
    storeid = json['storeid'];
    icon = json['icon'];
    storeReference = json['store_reference'];
    storehandle = json['storehandle'];
    storeName = json['store_name'];
    storeDesc = json['store_desc'];
    isDeleted = json['deleted'];
    openForOrder = json['open_for_order'];
    sellerNote = json['sellerNote']??"";
    trustCenter = json['trust_center'] != null
        ? TrustCenter.fromJson(json['trust_center'])
        : null;
    if (json['products'] != null) {
      cartProductList = <CartProduct>[];
      json['products'].forEach((v) {
        cartProductList!.add(CartProduct.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['storeid'] = storeid;
    data['icon'] = icon;
    data['store_reference'] = storeReference;
    data['storehandle'] = storehandle;
    data['store_name'] = storeName;
    data['store_desc'] = storeDesc;
    data['deleted'] = isDeleted;
    data['open_for_order'] = openForOrder;
    data['sellerNote'] = sellerNote;
    if (trustCenter != null) {
      data['trust_center'] = trustCenter!.toJson();
    }
    if (cartProductList != null) {
      data['products'] = cartProductList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TrustCenter {
  int? trustscore;
  String? sellerlevel;

  TrustCenter({this.trustscore, this.sellerlevel});

  TrustCenter.fromJson(Map<String, dynamic> json) {
    trustscore = json['trustscore'];
    sellerlevel = json['sellerlevel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['trustscore'] = trustscore;
    data['sellerlevel'] = sellerlevel;
    return data;
  }
}

class CartProduct {
  int? productid;
  String? productReference;
  String? productName;
  String? brandName;
  String? productDescription;
  String? productVersion;
  int? createdBy;
  int? modifiedBy;
  int? inStock;
  int? mrpPrice;
  int? sellingPrice;
  String? prodImages;
  String? refundWarranty;
  bool? isWishlisted;
  Cart? cart;
  bool? isDeleted;
  bool? deliverability;

  // Variant information
  String? variantReference;
  Map<String, String>? variantCombinations;
  Map<String, List<String>>? productOptions;

  CartProduct(
      {this.productid,
        this.productReference,
        this.productName,
        this.brandName,
        this.productDescription,
        this.productVersion,
        this.createdBy,
        this.modifiedBy,
        this.inStock,
        this.mrpPrice,
        this.sellingPrice,
        this.prodImages,
        this.refundWarranty,
        this.isWishlisted,
        this.isDeleted,
        this.deliverability,
        this.cart,
        this.variantReference,
        this.variantCombinations,
        this.productOptions});

  CartProduct.fromJson(Map<String, dynamic> json) {
    productid = json['productid'];
    productReference = json['product_reference'];
    productName = json['product_name'];
    brandName = json['brand_name'];
    productDescription = json['product_description'];
    productVersion = json['product_version'];
    createdBy = json['created_by'];
    modifiedBy = json['modified_by'];
    inStock = json['in_stock'];
    mrpPrice = json['mrp_price'];
    sellingPrice = json['selling_price'];
    prodImages = json['prod_images'];
    refundWarranty = json['refund_warranty'];
    isDeleted = json['deleted'];
    deliverability = json['deliverability'];
    isWishlisted = json['is_wishlisted'];
    cart = json['cart'] != null ? Cart.fromJson(json['cart']) : null;

    // Parse variant information
    variantReference = json['variant_reference'];
    if (json['variant_combinations'] != null) {
      variantCombinations = Map<String, String>.from(json['variant_combinations']);
    }
    if (json['product_options'] != null) {
      productOptions = Map<String, List<String>>.from(
        json['product_options'].map((key, value) =>
          MapEntry(key, List<String>.from(value))
        )
      );
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productid'] = productid;
    data['product_reference'] = productReference;
    data['product_name'] = productName;
    data['brand_name'] = brandName;
    data['product_description'] = productDescription;
    data['product_version'] = productVersion;
    data['created_by'] = createdBy;
    data['modified_by'] = modifiedBy;
    data['in_stock'] = inStock;
    data['mrp_price'] = mrpPrice;
    data['selling_price'] = sellingPrice;
    data['prod_images'] = prodImages;
    data['refund_warranty'] = refundWarranty;
    data['deleted'] = isDeleted;
    data['deliverability'] = deliverability;
    data['is_wishlisted'] = isWishlisted;
    if (cart != null) {
      data['cart'] = cart!.toJson();
    }

    // Add variant information
    data['variant_reference'] = variantReference;
    data['variant_combinations'] = variantCombinations;
    data['product_options'] = productOptions;

    return data;
  }
}

class Cart {
  int? cartItemId;
  int? quantity;
  String? cartStatus;

  Cart({this.cartItemId, this.quantity, this.cartStatus});

  Cart.fromJson(Map<String, dynamic> json) {
    cartItemId = json['cart_item_id'];
    quantity = json['quantity'];
    cartStatus = json['cart_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['cart_item_id'] = cartItemId;
    data['quantity'] = quantity;
    data['cart_status'] = cartStatus;
    return data;
  }
}
