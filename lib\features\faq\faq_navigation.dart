import 'package:flutter/material.dart';
import 'package:swadesic/features/faq/faq_screen.dart';
import 'package:swadesic/util/app_constants.dart';

class FaqNavigation {
  static void navigateToFaqScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const FaqScreen()),
    );
  }

  // Navigate to FAQ screen with specific category
  static void navigateToFaqCategory(BuildContext context, String categoryId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FaqScreen(
          initialCategoryId: categoryId,
        ),
      ),
    );
  }

  // Navigate to FAQ screen with specific question expanded
  static void navigateToFaqQuestion(
    BuildContext context,
    String categoryId,
    String questionId,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FaqScreen(
          initialCategoryId: categoryId,
          initialQuestionId: questionId,
        ),
      ),
    );
  }

  // Generate FAQ category link
  static String generateCategoryLink(String categoryId) {
    return '${AppConstants.domainName}faq?category=$categoryId';
  }

  // Generate FAQ question link
  static String generateQuestionLink(String categoryId, String questionId) {
    return '${AppConstants.domainName}faq?category=$categoryId&question=$questionId';
  }

  // Parse FAQ link and navigate
  static void handleFaqLink(BuildContext context, Uri uri) {
    final categoryId = uri.queryParameters['category'];
    final questionId = uri.queryParameters['question'];

    if (categoryId != null && questionId != null) {
      // Navigate to specific question
      navigateToFaqQuestion(context, categoryId, questionId);
    } else if (categoryId != null) {
      // Navigate to specific category
      navigateToFaqCategory(context, categoryId);
    } else {
      // Navigate to general FAQ screen
      navigateToFaqScreen(context);
    }
  }
}
