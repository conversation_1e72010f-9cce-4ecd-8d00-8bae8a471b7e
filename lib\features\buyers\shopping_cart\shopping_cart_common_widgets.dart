import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_field_style.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/variant_selection_bottom_sheet.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';

class ShoppingCartCommonWidgets {
  //region Store drop down
  static Widget storeDropDOwn(
      {required CartStore cartStore,
      required BuildContext context,
      required onTapDropDown,
      required ShoppingCartBloc shoppingCartBloc}) {
    return StreamBuilder<CartDetailState>(
        stream: shoppingCartBloc.cartDetailStateCtrl.stream,
        builder: (context, snapshot) {
          return Container(
            margin: const EdgeInsets.only(bottom: 1, left: 7, right: 7),
            padding: const EdgeInsets.only(bottom: 20),
            decoration: const BoxDecoration(
              color: AppColors.appWhite,
              border: Border(
                bottom: BorderSide(
                  color: AppColors
                      .textFieldFill1, // Specify the desired border color
                  width: 1.0, // Specify the desired border width
                ),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ///Drop down
                Container(
                  margin: const EdgeInsets.only(top: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //Store handle and icon
                      InkWell(
                        onTap: () {
                          onTapDropDown();
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            //Store Icon and name
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                InkWell(
                                  onTap: () {
                                    shoppingCartBloc.goToStore(
                                        storeReference:
                                            cartStore.storeReference!);
                                  },
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: SizedBox(
                                        height: 30,
                                        width: 30,
                                        child: extendedImage(
                                            cartStore.icon, context, 30, 30,
                                            fit: BoxFit.fill,
                                            customPlaceHolder:
                                                AppImages.storePlaceHolder)),
                                  ),
                                ),
                                Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 10),
                                  child: Text(
                                    cartStore.storehandle!,
                                    style: AppTextStyle.contentHeading0(
                                        textColor: AppColors.appBlack),
                                  ),
                                ),

                                ///Store closed
                                Visibility(
                                    visible: !cartStore.openForOrder!,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      decoration: BoxDecoration(
                                          color: AppColors.textFieldFill2,
                                          borderRadius:
                                              BorderRadius.circular(5)),
                                      child: Text(
                                        AppStrings.notOpenForOrderNow,
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyle.smallText(
                                            textColor: AppColors.orange),
                                      ),
                                    )),

                                ///Store deleted
                                Visibility(
                                    visible: cartStore.isDeleted!,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      decoration: BoxDecoration(
                                          color: AppColors.textFieldFill2,
                                          borderRadius:
                                              BorderRadius.circular(5)),
                                      child: Text(
                                        AppStrings.noLongerAvailable,
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyle.smallText(
                                            textColor: AppColors.red),
                                      ),
                                    )),
                              ],
                            ),
                            //Expanded(child: horizontalSizedBox(10)),
                            RotatedBox(
                                quarterTurns: cartStore.isDropDown ? 0 : 2,
                                child: const Icon(
                                  Icons.keyboard_arrow_up_rounded,
                                  color: AppColors.appBlack,
                                  weight: 2,
                                  size: 30,
                                ))
                          ],
                        ),
                      ),
                      //Add to wishlist and removed from cart
                      Container(
                        margin: const EdgeInsets.only(top: 10, bottom: 5),
                        child: Row(
                          children: [
                            ///Add to wishlist
                            Visibility(
                              visible: !cartStore.openForOrder!,
                              child: Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 10),
                                child: InkWell(
                                  onTap: () async {
                                    //Add to wish list
                                    await shoppingCartBloc.addToWishListApi(
                                        productReferenceList: cartStore
                                            .cartProductList!
                                            .map((e) => e.productReference!)
                                            .toList());
                                    //Remove product from cart
                                    await shoppingCartBloc
                                        .removeMultipleCartItemsApi(
                                            cartItemIdList: cartStore
                                                .cartProductList!
                                                .map((e) => e.cart!.cartItemId!)
                                                .toList());
                                    //Get cart items
                                    await shoppingCartBloc.getCartItems();
                                    //Get cart price
                                    await shoppingCartBloc
                                        .getShoppingCartPrice();
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 5),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: AppColors.textFieldFill1,
                                    ),
                                    child: Text(
                                      AppStrings.moveToWishList,
                                      style: AppTextStyle.smallText(
                                          textColor: AppColors.appBlack),
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            ///Remove from cart
                            Visibility(
                              visible: !cartStore.openForOrder! ||
                                  cartStore.isDeleted!,
                              child: Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 10),
                                child: InkWell(
                                  onTap: () async {
                                    //Remove product from cart
                                    await shoppingCartBloc
                                        .removeMultipleCartItemsApi(
                                            cartItemIdList: cartStore
                                                .cartProductList!
                                                .map((e) => e.cart!.cartItemId!)
                                                .toList());
                                    //Get cart items
                                    await shoppingCartBloc.getCartItems();
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 5),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: AppColors.textFieldFill1,
                                    ),
                                    child: Text(
                                      AppStrings.removeFromCart,
                                      style: AppTextStyle.smallText(
                                          textColor: AppColors.appBlack),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      //Product image
                      Visibility(
                        visible: !cartStore.isDropDown,
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 40),
                          child: Wrap(
                            alignment: WrapAlignment
                                .center, // Aligns items in the center
                            spacing: 10.0, // Spacing between items
                            runSpacing: 10.0, // Spacing between rows
                            children: cartStore.cartProductList!.map((item) {
                              return Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(11)),
                                height: 25,
                                width: 25,
                                child: CupertinoButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: () {
                                    var screen = BuyerViewSingleProductScreen(
                                      productReference: item.productReference!,
                                    );
                                    var route = MaterialPageRoute(
                                        builder: (context) => screen);
                                    Navigator.push(context, route);
                                  },
                                  child: ClipRRect(
                                      borderRadius: BorderRadius.circular(11),
                                      child: extendedImage(
                                          item.prodImages, context, 50, 50,
                                          imageHeight: 25,
                                          imageWidth: 25,
                                          customPlaceHolder:
                                              AppImages.productPlaceHolder)),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      )
                    ],
                  ),
                ),

                ///Add note and Product list
                Visibility(
                  visible: cartStore.isDropDown,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ///Add seller notes
                      addAndField(
                          cartStore: cartStore,
                          shoppingCartBloc: shoppingCartBloc),
                      //Product list
                      ListView.builder(
                          itemCount: cartStore.cartProductList!.length,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return productCard(
                                products: cartStore.cartProductList![index],
                                context: context,
                                shoppingCartBloc: shoppingCartBloc,
                                cartStore: cartStore);
                          }),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
  //endregion

  //region TextField or add seller notes button show
  static Widget addAndField(
      {required CartStore cartStore,
      required ShoppingCartBloc shoppingCartBloc}) {
    //If seller notes are empty and text field visibility is false
    if (cartStore.sellerNote!.isEmpty &&
        !cartStore.isSellerNoteTextFieldVisible) {
      return CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          cartStore.isSellerNoteTextFieldVisible = true;
          //Success
          shoppingCartBloc.cartDetailStateCtrl.sink
              .add(CartDetailState.Success);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(7),
              color: AppColors.textFieldFill1),
          margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 11),
          child: Text(
            AppStrings.addSellerNote,
            style: AppTextStyle.access0(textColor: AppColors.appBlack),
          ),
        ),
      );
    }
    //If field visible is true
    if (cartStore.isSellerNoteTextFieldVisible) {
      final TextEditingController notesTextCtrl =
          TextEditingController(text: cartStore.sellerNote);
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 11),
        child: Stack(
          children: [
            TextFormField(
              minLines: 5,
              maxLines: 5,
              // autofocus: false,
              controller: notesTextCtrl,
              style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack),
              keyboardType: TextInputType.text,
              textCapitalization: TextCapitalization.sentences,

              // onTap: (){
              //   // //print("Tapped");
              //   // final val = TextSelection.collapsed(offset: notesTextCtrl.text.length);
              //   // notesTextCtrl.selection = val;
              // },
              onChanged: (value) {
                shoppingCartBloc.isNoteChanged = true;
                cartStore.sellerNote = value;
              },
              decoration: InputDecoration(
                  isDense: true,
                  hintStyle:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  fillColor: AppColors
                      .textFieldFill1, // Specify the desired internal color
                  filled: true,
                  hintText: AppStrings.addSellerNote,
                  contentPadding: const EdgeInsets.symmetric(
                      vertical: 10.0, horizontal: 16.0),
                  border: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  focusedBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  enabledBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  disabledBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  focusedErrorBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  errorBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1)),
            ),
            Positioned(
                bottom: 12,
                right: 11,
                child: InkWell(
                  onTap: () {
                    //If field is null
                    if (notesTextCtrl.text.isNotEmpty) {
                      //Mark field visible to false
                      cartStore.isSellerNoteTextFieldVisible = false;
                      //Success
                      shoppingCartBloc.cartDetailStateCtrl.sink
                          .add(CartDetailState.Success);
                    }
                    //Add text to the seller notes.
                    cartStore.sellerNote = notesTextCtrl.text;
                    //Mark field visible to false
                    cartStore.isSellerNoteTextFieldVisible = false;
                    //Save notes to cache memory
                    shoppingCartBloc.saveMessageToSharePref();
                    //Success
                    shoppingCartBloc.cartDetailStateCtrl.sink
                        .add(CartDetailState.Success);
                    //print( cartStore.sellerNote);
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        color: AppColors.textFieldFill1,
                        borderRadius: BorderRadius.circular(8),
                        border:
                            Border.all(color: AppColors.appBlack, width: 1)),
                    child: Text(
                      AppStrings.add,
                      style: AppTextStyle.button2Bold(
                          textColor: AppColors.appBlack),
                    ),
                  ),
                ))
          ],
        ),
      );
    }
    //If seller notes is not empty and field visible is false
    if (cartStore.sellerNote!.isNotEmpty &&
        !cartStore.isSellerNoteTextFieldVisible) {
      return InkWell(
        onTap: () {
          //
          cartStore.isSellerNoteTextFieldVisible = true;
          //Success
          shoppingCartBloc.cartDetailStateCtrl.sink
              .add(CartDetailState.Success);
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 11),
          decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(10)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppStrings.sellerNotes,
                style: AppTextStyle.access0(textColor: AppColors.appBlack),
              ),
              verticalSizedBox(10),
              Text(
                cartStore.sellerNote!,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
            ],
          ),
        ),
      );
    }

    return Container();
  }

  //endregion

//region Product card
  static Widget productCard(
      {required CartProduct products,
      required BuildContext context,
      required ShoppingCartBloc shoppingCartBloc,
      required CartStore cartStore}) {
    return Opacity(
      opacity: products.isDeleted! ? 0.5 : 1.0,
      child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///Unable to deliver to the address
              Visibility(
                  visible: !products.deliverability!,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    decoration: BoxDecoration(
                        color: AppColors.textFieldFill2,
                        borderRadius: BorderRadius.circular(5)),
                    child: Text(
                      AppStrings.notDeliverable,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: AppTextStyle.smallText(textColor: AppColors.red),
                    ),
                  )),

              ///About product and image
              Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ///Brand name
                        Text(
                          products.brandName!,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack),
                        ),
                        verticalSizedBox(5),

                        ///Product name
                        Container(
                          alignment: Alignment.centerLeft,
                          // height: 44,
                          child: Text(
                            products.productName!,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack),
                          ),
                        ),

                        ///Variant selection (if variants exist)
                        if (_hasVariants(products))
                          Container(
                            margin: const EdgeInsets.only(top: 8),
                            child: _buildVariantSelector(
                              products: products,
                              context: context,
                              shoppingCartBloc: shoppingCartBloc,
                              cartStore: cartStore,
                            ),
                          ),
                      ],
                    ),
                  ),
                  horizontalSizedBox(12),
                  InkWell(
                    onTap: () {
                      var screen = BuyerViewSingleProductScreen(
                          productReference: products.productReference);
                      // var screen = const MyOrdersScreen();
                      var route =
                          MaterialPageRoute(builder: (context) => screen);
                      Navigator.push(context, route);
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: SizedBox(
                        height: 70,
                        width: 70,
                        child:
                            extendedImage(products.prodImages, context, 70, 70),
                      ),
                    ),
                  ),
                ],
              ),

              ///Price and quantity
              Container(
                // height: 25,
                margin: const EdgeInsets.symmetric(vertical: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ///Selling and printed price (use variant pricing if available)
                    Text(
                      "₹ ${_getDisplaySellingPrice(products)}",
                      style:
                          AppTextStyle.access0(textColor: AppColors.appBlack),
                    ),
                    horizontalSizedBox(6),
                    Text(
                      "₹ ${_getDisplayMrpPrice(products)}",
                      style: AppTextStyle.access0(
                          textColor: AppColors.writingBlack1,
                          isLineThrough: true),
                    ),
                    Expanded(child: horizontalSizedBox(10)),

                    ///Quantity and drop down
                    Row(
                      children: [
                        Text(
                          "Quantity :",
                          style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack),
                        ),
                        IgnorePointer(
                          ignoring: products.isDeleted!,
                          child: Container(
                            // width: 60,
                            height: 30,
                            margin: const EdgeInsets.only(left: 10),
                            padding: const EdgeInsets.only(left: 10, right: 10),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(50),
                                color: AppColors.appWhite,
                                border: Border.all(color: AppColors.appBlack)),
                            child: DropdownButton<int>(
                              isExpanded: false,
                              underline: const SizedBox(),
                              value: products.cart!.quantity!,
                              iconEnabledColor: AppColors.appBlack,
                              iconDisabledColor: AppColors.appBlack,
                              icon: const Icon(Icons.keyboard_arrow_down),
                              borderRadius: BorderRadius.circular(10),
                              items: ShoppingCartBloc.quantityList
                                  .map<DropdownMenuItem<int>>((int value) {
                                return DropdownMenuItem(
                                  value: value,
                                  child: Text(
                                    value.toString(),
                                    style: AppTextStyle.contentText0(
                                        textColor: AppColors.appBlack),
                                  ),
                                  // child: appText(
                                  //   value.toString(),
                                  //   color: AppColors.writingColor3,
                                  //   fontFamily: AppConstants.rRegular,
                                  //   fontWeight: FontWeight.w600,
                                  //   fontSize: 14,
                                  // ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                shoppingCartBloc.onChangeQuantity(
                                    products: products, quantity: value!);
                              },
                            ),

                            // child: appText(widget.sellerReturnStoreWarrantyBloc.returnPeriod.toString(),color: AppColors.appBlack,fontFamily: AppConstants.rRegular,
                            // fontWeight: FontWeight.w400,fontSize: 16,
                            //
                            // ),
                          ),
                        ),
                        InkWell(
                            onTap: () {
                              shoppingCartBloc.deleteShoppingCartItem(
                                  cartStore: cartStore, products: products);
                            },
                            child: Container(
                                margin: const EdgeInsets.only(left: 10),
                                height: 24,
                                width: 24,
                                child: SvgPicture.asset(
                                  AppImages.delete,
                                  height: 24,
                                  width: 24,
                                )))
                      ],
                    )
                  ],
                ),
              ),

              ///Return accept
              Container(
                alignment: Alignment.centerLeft,
                margin: const EdgeInsets.only(bottom: 20),
                // height: 20,
                child: Text(
                  products.refundWarranty!,
                  style: AppTextStyle.contentText0(
                      textColor: products.refundWarranty!
                              .contains("no returns accepted")
                          ? AppColors.orange
                          : AppColors.brandBlack),
                ),
                // child: appText(
                //   products.refundWarranty!,
                //   fontFamily: AppConstants.rRegular,
                //   color: products.refundWarranty!.contains("no returns accepted") ? AppColors.orange : AppColors.discountGreen,
                //   fontSize: 14,
                //   fontWeight: FontWeight.w400,
                // ),
              ),
            ],
          )),
    );
  }
//endregion

  //region Variant Selection Helper Methods
  static bool _hasVariants(CartProduct products) {
    return products.options != null &&
           products.options!.isNotEmpty &&
           products.productVariants != null &&
           products.productVariants!.isNotEmpty;
  }

  static Widget _buildVariantSelector({
    required CartProduct products,
    required BuildContext context,
    required ShoppingCartBloc shoppingCartBloc,
    required CartStore cartStore,
  }) {
    return GestureDetector(
      onTap: () => _showVariantSelectionBottomSheet(
        products: products,
        context: context,
        shoppingCartBloc: shoppingCartBloc,
        cartStore: cartStore,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.borderColor1),
        ),
        child: Row(
          children: [
            Text(
              "Selected: ",
              style: AppTextStyle.smallText(textColor: AppColors.appBlack),
            ),
            Expanded(
              child: Text(
                _getVariantDisplayText(products),
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            const Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.appBlack,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  static String _getVariantDisplayText(CartProduct products) {
    // Find the selected variant based on selected variant reference
    if (products.selectedVariantReference != null && products.productVariants != null) {
      final selectedVariant = products.productVariants!.firstWhere(
        (variant) => variant.variantReference == products.selectedVariantReference,
        orElse: () => products.productVariants!.first,
      );

      if (selectedVariant.combinations.isNotEmpty) {
        List<String> displayParts = [];
        selectedVariant.combinations.forEach((key, value) {
          displayParts.add("$key: $value");
        });
        return displayParts.join(", ");
      }
    }
    return "Standard";
  }

  static int _getDisplaySellingPrice(CartProduct products) {
    // Use selected variant selling price if available, otherwise use product selling price
    return products.selectedVariantSellingPrice ?? products.sellingPrice ?? 0;
  }

  static int _getDisplayMrpPrice(CartProduct products) {
    // Use selected variant MRP price if available, otherwise use product MRP price
    return products.selectedVariantMrpPrice ?? products.mrpPrice ?? 0;
  }

  static void _showVariantSelectionBottomSheet({
    required CartProduct products,
    required BuildContext context,
    required ShoppingCartBloc shoppingCartBloc,
    required CartStore cartStore,
  }) {
    // Create a product object using the cart data (which has all variant information)
    Product productWithVariants = Product(
      productReference: products.productReference,
      productName: products.productName,
      brandName: products.brandName,
      mrpPrice: products.mrpPrice,
      sellingPrice: products.sellingPrice,
      inStock: products.inStock,
      options: products.options,
      // Convert ProductVariantData to the format expected by Product model
      variants: products.productVariants?.map((variant) => {
        'product_variantid': variant.productVariantId,
        'variant_reference': variant.variantReference,
        'product_reference': variant.productReferenceId,
        'combinations': variant.combinations,
        'mrp_price': variant.mrpPrice,
        'selling_price': variant.sellingPrice,
        'stock': variant.stock,
        'is_active': variant.isActive,
        'created_date': variant.createdDate,
        'modified_date': variant.modifiedDate,
        'variant_version': variant.variantVersion,
      }).toList(),
    );

    // Create a ProductDetailFullCardBloc with the cart data
    ProductDetailFullCardBloc tempBloc = ProductDetailFullCardBloc(
      context,
      productWithVariants,
      false, // isFromAddProduct
    );
    tempBloc.init();

    // Set the currently selected variant if available
    if (products.selectedVariantReference != null && products.productVariants != null) {
      final selectedVariant = products.productVariants!.firstWhere(
        (variant) => variant.variantReference == products.selectedVariantReference,
        orElse: () => products.productVariants!.first,
      );

      // Convert to ProductVariant format
      tempBloc.selectedVariant = ProductVariant(
        productVariantId: selectedVariant.productVariantId,
        variantReference: selectedVariant.variantReference,
        productReference: selectedVariant.productReferenceId,
        combinations: selectedVariant.combinations,
        mrpPrice: selectedVariant.mrpPrice,
        sellingPrice: selectedVariant.sellingPrice,
        stock: selectedVariant.stock,
        isActive: selectedVariant.isActive,
        createdDate: selectedVariant.createdDate,
        modifiedDate: selectedVariant.modifiedDate,
        variantVersion: selectedVariant.variantVersion,
      );
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => VariantSelectionBottomSheet(
        product: productWithVariants,
        productDetailFullCardBloc: tempBloc,
        onVariantSelected: (variant) {
          _handleVariantChange(
            products: products,
            newVariant: variant,
            shoppingCartBloc: shoppingCartBloc,
            cartStore: cartStore,
            context: context,
          );
        },
      ),
    );
  }

  static void _handleVariantChange({
    required CartProduct products,
    required ProductVariant newVariant,
    required ShoppingCartBloc shoppingCartBloc,
    required CartStore cartStore,
    required BuildContext context,
  }) async {
    try {
      // Remove old variant from cart
      await shoppingCartBloc.deleteShoppingCartItem(
        cartStore: cartStore,
        products: products,
      );

      // Add new variant to cart
      await ShoppingCartServices().addToCart(
        productReference: products.productReference!,
        storeId: cartStore.storeid!,
        variantReference: newVariant.variantReference,
      );

      // Refresh cart
      await shoppingCartBloc.getCartItems();
      await shoppingCartBloc.getShoppingCartPrice();

      // Show success message
      if (context.mounted) {
        CommonMethods.toastMessage(
          "Variant updated successfully",
          context,
        );
      }
    } catch (e) {
      // Show error message
      if (context.mounted) {
        CommonMethods.toastMessage(
          "Failed to update variant",
          context,
        );
      }
    }
  }
  //endregion
}
