import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreReviewSubmittedSuccessScreen extends StatefulWidget {
  final String storeName;
  final String storeHandle;

  const StoreReviewSubmittedSuccessScreen({
    Key? key,
    required this.storeName,
    required this.storeHandle,
  }) : super(key: key);

  @override
  StoreReviewSubmittedSuccessScreenState createState() =>
      StoreReviewSubmittedSuccessScreenState();
}

class StoreReviewSubmittedSuccessScreenState
    extends State<StoreReviewSubmittedSuccessScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Top section with close button
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.lightGray.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: AppColors.appBlack,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),

              // Main content
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Success icon
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.green.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check_circle,
                        color: AppColors.green,
                        size: 80,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Success title
                    Text(
                      'Review Submitted Successfully!',
                      style: AppTextStyle.heading1Bold(
                        textColor: AppColors.appBlack,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // Success message
                    Text(
                      'Thank you for reviewing ${widget.storeName}. Your feedback helps other customers discover great Swadeshi businesses.',
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 32),

                    // Store info
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.lightGray.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.brandBlack,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.store,
                              color: AppColors.appWhite,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.storeName,
                                  style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.appBlack,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  '@${widget.storeHandle}',
                                  style: AppTextStyle.smallText(
                                    textColor: AppColors.writingBlack1,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Bottom buttons
              Column(
                children: [
                  // Explore Swadesic button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Navigate to explore or home screen
                        _onTapExploreSwadesic();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.brandBlack,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Explore Swadesic',
                        style: AppTextStyle.heading3Medium(
                          textColor: AppColors.appWhite,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Share review button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        _onTapShareReview();
                      },
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.brandBlack),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Share Your Review',
                        style: AppTextStyle.heading3Medium(
                          textColor: AppColors.brandBlack,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onTapExploreSwadesic() {
    // Navigate to home/explore screen
    // For now, just close the screen
    Navigator.of(context).pop();
  }

  void _onTapShareReview() {
    // Share the store or review
    String shareText =
        'I just reviewed ${widget.storeName} on Swadesic! Check out this amazing Swadeshi business: https://lol.swadesic.com/${widget.storeHandle}';

    CommonMethods.share(shareText);
  }
}
